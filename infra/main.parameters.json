{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"environmentName": {"value": "${AZURE_ENV_NAME}"}, "resourceGroupName": {"value": "${AZURE_RESOURCE_GROUP}"}, "location": {"value": "${AZURE_LOCATION}"}, "openAiServiceName": {"value": "${AZURE_OPENAI_SERVICE}"}, "openAiResourceGroupName": {"value": "${AZURE_OPENAI_RESOURCE_GROUP}"}, "openAiResourceGroupLocation": {"value": "${AZURE_OPENAI_SERVICE_LOCATION=eastus}"}, "openAiSkuName": {"value": "S0"}, "documentIntelligenceServiceName": {"value": "${AZURE_DOCUMENT_INTELLIGENCE_SERVICE}"}, "documentIntelligenceResourceGroupName": {"value": "${AZURE_DOCUMENT_INTELLIGENCE_RESOURCE_GROUP}"}, "documentIntelligenceResourceGroupLocation": {"value": "${AZURE_DOCUMENT_INTELLIGENCE_RESOURCE_GROUP_LOCATION=eastus}"}, "documentIntelligenceSkuName": {"value": "S0"}, "storageAccountName": {"value": "${AZURE_STORAGE_ACCOUNT}"}, "storageResourceGroupName": {"value": "${AZURE_STORAGE_RESOURCE_GROUP}"}, "storageSkuName": {"value": "${AZURE_STORAGE_SKU=Standard_LRS}"}, "chatGptModelName": {"value": "${AZURE_OPENAI_CHATGPT_MODEL=gpt-4o}"}, "chatGptModelVersion": {"value": "${AZURE_OPENAI_CHATGPT_VERSION=2024-11-20}"}, "chatGptDeploymentName": {"value": "${AZURE_OPENAI_CHATGPT_DEPLOYMENT=gpt-4o}"}, "chatGptDeploymentCapacity": {"value": "${AZURE_OPENAI_CHATGPT_DEPLOYMENT_CAPACITY=30}"}, "chatGptDeploymentSkuName": {"value": "${AZURE_OPENAI_CHATGPT_DEPLOYMENT_SKU_NAME=GlobalStandard}"}, "useApplicationInsights": {"value": "${AZURE_USE_APPLICATION_INSIGHTS=true}"}, "copilotAppExists": {"value": false}, "webAppExists": {"value": false}, "accountAppExists": {"value": false}, "paymentAppExists": {"value": false}, "transactionAppExists": {"value": false}}}