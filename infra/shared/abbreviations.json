{"analysisServicesServers": "as", "apiManagementService": "apim-", "appConfigurationConfigurationStores": "appcs-", "appManagedEnvironments": "cae-", "appContainerApps": "ca-", "authorizationPolicyDefinitions": "policy-", "automationAutomationAccounts": "aa-", "blueprintBlueprints": "bp-", "blueprintBlueprintsArtifacts": "bpa-", "cacheRedis": "redis-", "cdnProfiles": "cdnp-", "cdnProfilesEndpoints": "cdne-", "cognitiveServicesAccounts": "cog-", "cognitiveServicesFormRecognizer": "cog-fr-", "cognitiveServicesTextAnalytics": "cog-ta-", "computeAvailabilitySets": "avail-", "computeCloudServices": "cld-", "computeDiskEncryptionSets": "des", "computeDisks": "disk", "computeDisksOs": "osdisk", "computeGalleries": "gal", "computeSnapshots": "snap-", "computeVirtualMachines": "vm", "computeVirtualMachineScaleSets": "vmss-", "containerInstanceContainerGroups": "ci", "containerRegistryRegistries": "cr", "containerServiceManagedClusters": "aks-", "databricksWorkspaces": "dbw-", "dataFactoryFactories": "adf-", "dataLakeAnalyticsAccounts": "dla", "dataLakeStoreAccounts": "dls", "dataMigrationServices": "dms-", "dBforMySQLServers": "mysql-", "dBforPostgreSQLServers": "psql-", "devicesIotHubs": "iot-", "devicesProvisioningServices": "provs-", "devicesProvisioningServicesCertificates": "pcert-", "documentDBDatabaseAccounts": "cosmos-", "eventGridDomains": "evgd-", "eventGridDomainsTopics": "evgt-", "eventGridEventSubscriptions": "evgs-", "eventHubNamespaces": "evhns-", "eventHubNamespacesEventHubs": "evh-", "hdInsightClustersHadoop": "hadoop-", "hdInsightClustersHbase": "hbase-", "hdInsightClustersKafka": "kafka-", "hdInsightClustersMl": "mls-", "hdInsightClustersSpark": "spark-", "hdInsightClustersStorm": "storm-", "hybridComputeMachines": "arcs-", "insightsActionGroups": "ag-", "insightsComponents": "appi-", "keyVaultVaults": "kv-", "kubernetesConnectedClusters": "arck", "kustoClusters": "dec", "kustoClustersDatabases": "dedb", "logicIntegrationAccounts": "ia-", "logicWorkflows": "logic-", "machineLearningServicesWorkspaces": "mlw-", "managedIdentityUserAssignedIdentities": "id-", "managementManagementGroups": "mg-", "migrateAssessmentProjects": "migr-", "networkApplicationGateways": "agw-", "networkApplicationSecurityGroups": "asg-", "networkAzureFirewalls": "afw-", "networkBastionHosts": "bas-", "networkConnections": "con-", "networkDnsZones": "dnsz-", "networkExpressRouteCircuits": "erc-", "networkFirewallPolicies": "afwp-", "networkFirewallPoliciesWebApplication": "waf", "networkFirewallPoliciesRuleGroups": "wafrg", "networkFrontDoors": "fd-", "networkFrontdoorWebApplicationFirewallPolicies": "fdfp-", "networkLoadBalancersExternal": "lbe-", "networkLoadBalancersInternal": "lbi-", "networkLoadBalancersInboundNatRules": "rule-", "networkLocalNetworkGateways": "lgw-", "networkNatGateways": "ng-", "networkNetworkInterfaces": "nic-", "networkNetworkSecurityGroups": "nsg-", "networkNetworkSecurityGroupsSecurityRules": "nsgsr-", "networkNetworkWatchers": "nw-", "networkPrivateDnsZones": "pdnsz-", "networkPrivateLinkServices": "pl-", "networkPublicIPAddresses": "pip-", "networkPublicIPPrefixes": "ippre-", "networkRouteFilters": "rf-", "networkRouteTables": "rt-", "networkRouteTablesRoutes": "udr-", "networkTrafficManagerProfiles": "traf-", "networkVirtualNetworkGateways": "vgw-", "networkVirtualNetworks": "vnet-", "networkVirtualNetworksSubnets": "snet-", "networkVirtualNetworksVirtualNetworkPeerings": "peer-", "networkVirtualWans": "vwan-", "networkVpnGateways": "vpng-", "networkVpnGatewaysVpnConnections": "vcn-", "networkVpnGatewaysVpnSites": "vst-", "notificationHubsNamespaces": "ntfns-", "notificationHubsNamespacesNotificationHubs": "ntf-", "operationalInsightsWorkspaces": "log-", "portalDashboards": "dash-", "powerBIDedicatedCapacities": "pbi-", "purviewAccounts": "pview-", "recoveryServicesVaults": "rsv-", "resourcesResourceGroups": "rg-", "searchSearchServices": "srch-", "serviceBusNamespaces": "sb-", "serviceBusNamespacesQueues": "sbq-", "serviceBusNamespacesTopics": "sbt-", "serviceEndPointPolicies": "se-", "serviceFabricClusters": "sf-", "signalRServiceSignalR": "sigr", "sqlManagedInstances": "sqlmi-", "sqlServers": "sql-", "sqlServersDataWarehouse": "sqldw-", "sqlServersDatabases": "sqldb-", "sqlServersDatabasesStretch": "sqlstrdb-", "storageStorageAccounts": "st", "storageStorageAccountsVm": "stvm", "storSimpleManagers": "ssimp", "streamAnalyticsCluster": "asa-", "synapseWorkspaces": "syn", "synapseWorkspacesAnalyticsWorkspaces": "synw", "synapseWorkspacesSqlPoolsDedicated": "syndp", "synapseWorkspacesSqlPoolsSpark": "synsp", "timeSeriesInsightsEnvironments": "tsi-", "webServerFarms": "plan-", "webSitesAppService": "app-", "webSitesAppServiceEnvironment": "ase-", "webSitesFunctions": "func-", "webStaticSites": "stapp-"}