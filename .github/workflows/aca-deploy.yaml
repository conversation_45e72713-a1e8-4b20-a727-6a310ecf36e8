name: Reusable Azure Container Apps deploy
on:
    workflow_call:
      inputs:
        env-name:
          required: true
          type: string
        image-name:
          required: true
          type: string
        container-app-name:
          required: true
          type: string
        container-app-env-name:
          required: true
          type: string

# Set up permissions for deploying with secretless Azure federated credentials
# https://learn.microsoft.com/en-us/azure/developer/github/connect-from-azure?tabs=azure-portal%2Clinux#set-up-azure-login-with-openid-connect-authentication
permissions:
  id-token: write
  contents: read

jobs:
    deploy:
        runs-on: ubuntu-latest
        environment: ${{inputs.env-name}}
        steps:
        - name: Log in to Azure with service principal
          uses: azure/login@v2
          if: ${{ vars.AZURE_CLIENT_ID == '' }}
          with:
            creds: ${{ secrets.AZURE_CREDENTIALS }}
        - name: Log in with Azure (Federated Credentials)
          if: ${{ vars.AZURE_CLIENT_ID != '' }}
          uses: azure/login@v2
          with:
            client-id: ${{ vars.AZURE_CLIENT_ID }}
            tenant-id: ${{ vars.AZURE_TENANT_ID }}
            subscription-id: ${{ vars.AZURE_SUBSCRIPTION_ID }} 
        - name: Build and deploy Container App
          uses: azure/container-apps-deploy-action@v1
          with:
              acrName: ${{vars.ACR_NAME}}
              containerAppName: ${{inputs.container-app-name}}
              containerAppEnvironment: ${{inputs.container-app-env-name}}
              resourceGroup: ${{vars.RESOURCE_GROUP}}
              imageToDeploy: ${{vars.ACR_NAME}}.azurecr.io/${{inputs.image-name}}:${{github.sha}}