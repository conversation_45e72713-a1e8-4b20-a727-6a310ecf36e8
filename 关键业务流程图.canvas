{"nodes": [{"id": "user-request", "type": "text", "x": 100, "y": 50, "width": 200, "height": 80, "text": "# 用户请求\n\n用户发送聊天消息\n（文本或图片）", "color": "1"}, {"id": "chat-controller", "type": "text", "x": 400, "y": 50, "width": 220, "height": 100, "text": "# ChatController\n\n- 接收HTTP请求\n- 验证请求参数\n- 转换消息格式\n- 调用监督者智能体", "color": "2"}, {"id": "supervisor-analysis", "type": "text", "x": 700, "y": 50, "width": 250, "height": 120, "text": "# 监督者分析\n\n1. 构建内部聊天记忆\n2. 过滤工具执行消息\n3. 调用GPT模型分析意图\n4. 返回目标智能体名称", "color": "3"}, {"id": "agent-routing", "type": "text", "x": 400, "y": 200, "width": 220, "height": 100, "text": "# 智能体路由\n\n- 查找目标智能体\n- 单轮路由执行\n- 错误处理（none情况）", "color": "4"}, {"id": "account-flow", "type": "text", "x": 100, "y": 350, "width": 200, "height": 120, "text": "# 账户流程\n\n1. 获取用户账户列表\n2. 查询账户详情\n3. 获取支付方式\n4. HTML格式化展示", "color": "5"}, {"id": "transaction-flow", "type": "text", "x": 350, "y": 350, "width": 200, "height": 140, "text": "# 交易流程\n\n1. 解析查询条件\n2. 获取账户ID\n3. 搜索交易记录\n4. 按日期排序\n5. 表格化展示", "color": "6"}, {"id": "payment-flow", "type": "text", "x": 600, "y": 350, "width": 250, "height": 200, "text": "# 支付流程\n\n1. 图片OCR扫描（可选）\n2. 提取支付信息\n3. 用户确认数据\n4. 检查重复支付\n5. 验证资金充足性\n6. 确认受益人信息\n7. 执行支付\n8. 返回确认信息", "color": "7"}, {"id": "mcp-tool-execution", "type": "text", "x": 900, "y": 350, "width": 220, "height": 140, "text": "# MCP工具执行\n\n1. 工具规范匹配\n2. 参数提取验证\n3. HTTP SSE调用\n4. 结果处理\n5. 错误处理", "color": "8"}, {"id": "ocr-process", "type": "text", "x": 100, "y": 600, "width": 250, "height": 140, "text": "# OCR处理流程\n\n1. 图片上传到Blob Storage\n2. Azure Document Intelligence分析\n3. 提取发票数据\n4. 结构化数据返回\n5. 用户确认环节", "color": "9"}, {"id": "payment-validation", "type": "text", "x": 400, "y": 600, "width": 220, "height": 160, "text": "# 支付验证流程\n\n1. 检查历史支付记录\n2. 验证受益人信息\n3. 检查账户余额\n4. 验证支付方式\n5. 风险评估\n6. 用户最终确认", "color": "10"}, {"id": "response-formatting", "type": "text", "x": 700, "y": 600, "width": 200, "height": 120, "text": "# 响应格式化\n\n1. HTML表格生成\n2. 数据结构化\n3. 错误信息处理\n4. 用户友好展示", "color": "11"}], "edges": [{"id": "request-to-controller", "fromNode": "user-request", "toNode": "chat-controller", "label": "1. HTTP POST", "color": "2"}, {"id": "controller-to-supervisor", "fromNode": "chat-controller", "toNode": "supervisor-analysis", "label": "2. 调用监督者", "color": "3"}, {"id": "supervisor-to-routing", "fromNode": "supervisor-analysis", "toNode": "agent-routing", "label": "3. 返回智能体名称", "color": "4"}, {"id": "routing-to-account", "fromNode": "agent-routing", "toNode": "account-flow", "label": "4a. AccountAgent", "color": "5"}, {"id": "routing-to-transaction", "fromNode": "agent-routing", "toNode": "transaction-flow", "label": "4b. TransactionAgent", "color": "6"}, {"id": "routing-to-payment", "fromNode": "agent-routing", "toNode": "payment-flow", "label": "4c. PaymentAgent", "color": "7"}, {"id": "agents-to-mcp", "fromNode": "account-flow", "toNode": "mcp-tool-execution", "label": "5. MCP工具调用", "color": "8"}, {"id": "transaction-to-mcp", "fromNode": "transaction-flow", "toNode": "mcp-tool-execution", "label": "5. MCP工具调用", "color": "8"}, {"id": "payment-to-mcp", "fromNode": "payment-flow", "toNode": "mcp-tool-execution", "label": "5. MCP工具调用", "color": "8"}, {"id": "payment-to-ocr", "fromNode": "payment-flow", "toNode": "ocr-process", "label": "图片处理", "color": "9"}, {"id": "payment-to-validation", "fromNode": "payment-flow", "toNode": "payment-validation", "label": "支付验证", "color": "10"}, {"id": "flows-to-formatting", "fromNode": "account-flow", "toNode": "response-formatting", "label": "6. 格式化响应", "color": "11"}, {"id": "transaction-to-formatting", "fromNode": "transaction-flow", "toNode": "response-formatting", "label": "6. 格式化响应", "color": "11"}, {"id": "payment-to-formatting", "fromNode": "payment-flow", "toNode": "response-formatting", "label": "6. 格式化响应", "color": "11"}]}