# yaml-language-server: $schema=https://raw.githubusercontent.com/Azure/azure-dev/main/schemas/v1.0/azure.yaml.json

name: personal-finance-assistance-java
metadata:
  template: agent-openai-java-banking-assistant@1.0.0-alpha
services:
  copilot:
    project: app/copilot
    language: java
    host: containerapp
    docker:
      remoteBuild: true
  account:
    project: app/business-api/account
    language: java
    host: containerapp
    docker:
      remoteBuild: true
  payment:
    project: app/business-api/payment
    language: java
    host: containerapp
    docker:
      remoteBuild: true
  transaction:
    project: app/business-api/transactions-history
    language: java
    host: containerapp
    docker:
      remoteBuild: true
  web:
    project: app/frontend
    language: js
    host: containerapp
    docker:
      remoteBuild: true
