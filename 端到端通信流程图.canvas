{"nodes": [{"id": "user-query", "type": "text", "x": 50, "y": 50, "width": 150, "height": 80, "text": "# 用户查询\n\n\"我的账户余额\n是多少？\"", "color": "1"}, {"id": "react-frontend", "type": "text", "x": 250, "y": 50, "width": 150, "height": 100, "text": "# React前端\n\n```typescript\nfetch('/api/chat', {\n  method: 'POST',\n  body: JSON.stringify(\n    {messages: [...]})\n})\n```", "color": "2"}, {"id": "chat-controller", "type": "text", "x": 450, "y": 50, "width": 150, "height": 100, "text": "# ChatController\n\n```java\n@PostMapping(\"/api/chat\")\nResponseEntity<ChatResponse>\nopenAIAsk(ChatAppRequest)\n```", "color": "3"}, {"id": "supervisor-invoke", "type": "text", "x": 650, "y": 50, "width": 150, "height": 100, "text": "# 监督者调用\n\n```java\nList<ChatMessage> \nresponse = supervisorAgent\n  .invoke(chatHistory)\n```", "color": "4"}, {"id": "gpt-routing", "type": "text", "x": 250, "y": 200, "width": 150, "height": 100, "text": "# GPT路由分析\n\n```java\naiMessage = chatModel\n  .chat(request)\n// \"AccountAgent\"\n```", "color": "5"}, {"id": "account-agent", "type": "text", "x": 450, "y": 200, "width": 150, "height": 100, "text": "# 账户智能体\n\n```java\naccountAgent\n  .invoke(chatHistory)\n// ReAct循环开始\n```", "color": "6"}, {"id": "react-cycle", "type": "text", "x": 650, "y": 200, "width": 150, "height": 120, "text": "# ReAct循环\n\n```java\nwhile(aiMessage\n  .hasToolRequests()) {\n  executeTools();\n  updateMemory();\n  continue();\n}\n```", "color": "7"}, {"id": "tool-decision", "type": "text", "x": 50, "y": 350, "width": 150, "height": 100, "text": "# 工具决策\n\n```java\nToolExecutionRequest {\n  name: \"getAccountsByUserName\",\n  args: \"{\\\"userName\\\":\\\"bob\\\"}\"\n}\n```", "color": "8"}, {"id": "mcp-client", "type": "text", "x": 250, "y": 350, "width": 150, "height": 100, "text": "# MCP客户端\n\n```java\nmcpClient = tool2ClientMap\n  .get(\"getAccountsByUserName\");\nresult = mcpClient\n  .executeTool(request)\n```", "color": "9"}, {"id": "sse-transport", "type": "text", "x": 450, "y": 350, "width": 150, "height": 100, "text": "# SSE传输\n\n```java\nHttpMcpTransport\n  .sseUrl(accountServiceUrl)\n  .timeout(3hours)\n  .send(request)\n```", "color": "10"}, {"id": "account-service", "type": "text", "x": 650, "y": 350, "width": 150, "height": 120, "text": "# 账户服务\n\n```java\n@Tool\nList<Account> \ngetAccountsByUserName(\n  String userName) {\n  return service.findByUser(userName);\n}\n```", "color": "11"}, {"id": "business-logic", "type": "text", "x": 250, "y": 500, "width": 150, "height": 100, "text": "# 业务逻辑\n\n```java\naccountService\n  .findByUserName(\"bob\")\n// 查询数据库\n// 返回账户列表\n```", "color": "12"}, {"id": "response-chain", "type": "text", "x": 450, "y": 500, "width": 150, "height": 120, "text": "# 响应链路\n\n```java\nAccount[] accounts = [...]\n// JSON序列化\n// SSE响应\n// MCP客户端接收\n// 智能体处理\n```", "color": "13"}, {"id": "html-formatting", "type": "text", "x": 650, "y": 500, "width": 150, "height": 120, "text": "# HTML格式化\n\n```java\n// GPT生成HTML表格\n<table>\n  <tr><th>账户</th><th>余额</th></tr>\n  <tr><td>储蓄</td><td>$5000</td></tr>\n</table>\n```", "color": "14"}, {"id": "final-response", "type": "text", "x": 450, "y": 650, "width": 150, "height": 100, "text": "# 最终响应\n\n```java\nChatResponse.builder()\n  .message(htmlContent)\n  .build()\n// 返回给前端\n```", "color": "15"}], "edges": [{"id": "user-to-react", "fromNode": "user-query", "toNode": "react-frontend", "label": "1. 用户输入", "color": "2"}, {"id": "react-to-controller", "fromNode": "react-frontend", "toNode": "chat-controller", "label": "2. HTTP POST", "color": "3"}, {"id": "controller-to-supervisor", "fromNode": "chat-controller", "toNode": "supervisor-invoke", "label": "3. 调用监督者", "color": "4"}, {"id": "supervisor-to-gpt", "fromNode": "supervisor-invoke", "toNode": "gpt-routing", "label": "4. GPT分析", "color": "5"}, {"id": "gpt-to-account", "fromNode": "gpt-routing", "toNode": "account-agent", "label": "5. 路由到账户智能体", "color": "6"}, {"id": "account-to-react", "fromNode": "account-agent", "toNode": "react-cycle", "label": "6. 启动ReAct", "color": "7"}, {"id": "react-to-tool", "fromNode": "react-cycle", "toNode": "tool-decision", "label": "7. 工具决策", "color": "8"}, {"id": "tool-to-mcp", "fromNode": "tool-decision", "toNode": "mcp-client", "label": "8. MC<PERSON>调用", "color": "9"}, {"id": "mcp-to-sse", "fromNode": "mcp-client", "toNode": "sse-transport", "label": "9. SSE传输", "color": "10"}, {"id": "sse-to-service", "fromNode": "sse-transport", "toNode": "account-service", "label": "10. 服务调用", "color": "11"}, {"id": "service-to-business", "fromNode": "account-service", "toNode": "business-logic", "label": "11. 业务处理", "color": "12"}, {"id": "business-to-response", "fromNode": "business-logic", "toNode": "response-chain", "label": "12. 响应返回", "color": "13"}, {"id": "response-to-html", "fromNode": "response-chain", "toNode": "html-formatting", "label": "13. HTML格式化", "color": "14"}, {"id": "html-to-final", "fromNode": "html-formatting", "toNode": "final-response", "label": "14. 最终响应", "color": "15"}]}