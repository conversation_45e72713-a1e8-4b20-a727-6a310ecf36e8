{"nodes": [{"id": "agent-decision", "type": "text", "x": 100, "y": 50, "width": 200, "height": 100, "text": "# 智能体决策\n\n```java\naiMessage.\n  hasToolExecutionRequests()\n// GPT决定调用工具\n```", "color": "1"}, {"id": "tool-request", "type": "text", "x": 400, "y": 50, "width": 250, "height": 120, "text": "# 工具执行请求\n\n```java\nToolExecutionRequest {\n  name: \"getAccountDetails\",\n  arguments: \"{\\\"accountId\\\":\\\"1010\\\"}\"\n}\n```", "color": "2"}, {"id": "executor-selection", "type": "text", "x": 750, "y": 50, "width": 250, "height": 120, "text": "# 执行器选择\n\n```java\n// 优先检查扩展执行器\nvar toolExecutor = \n  extendedExecutorMap.get(toolName);\nif (toolExecutor != null) {\n  // 本地工具执行\n}\n```", "color": "3"}, {"id": "mcp-execution", "type": "text", "x": 400, "y": 200, "width": 250, "height": 140, "text": "# MCP工具执行\n\n```java\nvar mcpClient = \n  tool2ClientMap.get(toolName);\nif (mcpClient != null) {\n  result = mcpClient\n    .executeTool(toolRequest);\n}\n```", "color": "4"}, {"id": "sse-transport", "type": "text", "x": 100, "y": 350, "width": 200, "height": 120, "text": "# SSE传输\n\n```java\nHttpMcpTransport\n  .sseUrl(serverUrl)\n  .timeout(3hours)\n  .build()\n```", "color": "5"}, {"id": "server-processing", "type": "text", "x": 350, "y": 350, "width": 200, "height": 120, "text": "# 服务器处理\n\n```java\n@Tool\npublic Account \ngetAccountDetails(\n  String accountId) {\n  return service.get(id);\n}\n```", "color": "6"}, {"id": "response-handling", "type": "text", "x": 600, "y": 350, "width": 200, "height": 120, "text": "# 响应处理\n\n```java\nif (result == null) {\n  result = \"ok\";\n}\nToolExecutionResultMessage\n  .from(request, result)\n```", "color": "7"}, {"id": "error-handling", "type": "text", "x": 850, "y": 350, "width": 200, "height": 120, "text": "# 错误处理\n\n```java\nif (mcpClient == null) {\n  throw new \n    IllegalArgumentException(\n      \"No MCP executor found\");\n}\n```", "color": "8"}, {"id": "result-integration", "type": "text", "x": 400, "y": 500, "width": 250, "height": 120, "text": "# 结果集成\n\n```java\ninternalChatMemory.add(aiMessage);\ntoolExecutionResults\n  .forEach(internalChatMemory::add);\n// 继续ReAct循环\n```", "color": "9"}, {"id": "logging-monitoring", "type": "text", "x": 750, "y": 500, "width": 200, "height": 120, "text": "# 日志监控\n\n```java\nLOGGER.info(\n  \"Executing {} with params {}\",\n  toolName, arguments);\nLOGGER.info(\n  \"Response: {}\", result);\n```", "color": "10"}], "edges": [{"id": "decision-to-request", "fromNode": "agent-decision", "toNode": "tool-request", "label": "1. 生成请求", "color": "2"}, {"id": "request-to-selection", "fromNode": "tool-request", "toNode": "executor-selection", "label": "2. 选择执行器", "color": "3"}, {"id": "selection-to-mcp", "fromNode": "executor-selection", "toNode": "mcp-execution", "label": "3. MC<PERSON>执行", "color": "4"}, {"id": "mcp-to-sse", "fromNode": "mcp-execution", "toNode": "sse-transport", "label": "4. SSE传输", "color": "5"}, {"id": "sse-to-server", "fromNode": "sse-transport", "toNode": "server-processing", "label": "5. 服务器处理", "color": "6"}, {"id": "server-to-response", "fromNode": "server-processing", "toNode": "response-handling", "label": "6. 响应处理", "color": "7"}, {"id": "mcp-to-error", "fromNode": "mcp-execution", "toNode": "error-handling", "label": "错误分支", "color": "8"}, {"id": "response-to-integration", "fromNode": "response-handling", "toNode": "result-integration", "label": "7. 结果集成", "color": "9"}, {"id": "execution-to-logging", "fromNode": "mcp-execution", "toNode": "logging-monitoring", "label": "日志记录", "color": "10"}]}