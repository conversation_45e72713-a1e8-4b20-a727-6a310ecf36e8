{"nodes": [{"id": "user-input", "type": "text", "x": 100, "y": 50, "width": 200, "height": 80, "text": "# 用户输入\n\n\"我想查看账户余额\"\n\"帮我支付这张发票\"", "color": "1"}, {"id": "supervisor-init", "type": "text", "x": 400, "y": 50, "width": 250, "height": 100, "text": "# 监督者初始化\n\n```java\nMap<String, AgentMetadata> \n  agentsMetadata = agents.stream()\n  .collect(toMap(getName, getMetadata))\n```", "color": "2"}, {"id": "prompt-building", "type": "text", "x": 750, "y": 50, "width": 250, "height": 120, "text": "# 提示构建\n\n```java\nPromptTemplate.from(\n  SUPERVISOR_SYSTEM_MESSAGE)\n.apply(Map.of(\n  \"agentsMetadata\", metadata))\n```", "color": "3"}, {"id": "chat-memory", "type": "text", "x": 100, "y": 200, "width": 250, "height": 120, "text": "# 聊天记忆构建\n\n```java\nbuildInternalChat(chatHistory)\n// 过滤工具执行消息\n// 保留用户和助手消息\n```", "color": "4"}, {"id": "gpt-analysis", "type": "text", "x": 400, "y": 200, "width": 250, "height": 120, "text": "# GPT意图分析\n\n```java\nChatRequest request = \n  ChatRequest.builder()\n  .messages(internalChatMemory)\n  .build();\nAiMessage response = \n  chatModel.chat(request)\n```", "color": "5"}, {"id": "agent-selection", "type": "text", "x": 750, "y": 200, "width": 250, "height": 120, "text": "# 智能体选择\n\n```java\nString nextAgent = \n  aiMessage.text();\n// \"AccountAgent\"\n// \"PaymentAgent\"\n// \"TransactionHistoryAgent\"\n```", "color": "6"}, {"id": "routing-logic", "type": "text", "x": 400, "y": 350, "width": 250, "height": 140, "text": "# 路由逻辑\n\n```java\nif(\"none\".equals(nextAgent)) {\n  // 返回澄清消息\n} else {\n  Agent agent = agents.stream()\n    .filter(a -> a.getName()\n      .equals(nextAgent))\n    .findFirst().orElseThrow();\n}\n```", "color": "7"}, {"id": "agent-invoke", "type": "text", "x": 100, "y": 520, "width": 200, "height": 100, "text": "# 智能体调用\n\n```java\nreturn agent.invoke(\n  chatHistory);\n```", "color": "8"}, {"id": "error-handling", "type": "text", "x": 350, "y": 520, "width": 200, "height": 100, "text": "# 错误处理\n\n```java\n.orElseThrow(() -> \n  new AgentExecutionException(\n    \"Agent not found\"))\n```", "color": "9"}, {"id": "clarification", "type": "text", "x": 600, "y": 520, "width": 200, "height": 100, "text": "# 澄清处理\n\n```java\nAiMessage.builder()\n  .text(\"I'm not sure...\n   Can you clarify?\")\n  .build()\n```", "color": "10"}, {"id": "metadata-structure", "type": "text", "x": 850, "y": 350, "width": 250, "height": 140, "text": "# 智能体元数据\n\n```java\nAgentMetadata(\n  \"Personal financial advisor\n   for retrieving account info\",\n  List.of(\"RetrieveAccountInfo\",\n         \"DisplayAccountDetails\")\n)\n```", "color": "11"}], "edges": [{"id": "input-to-init", "fromNode": "user-input", "toNode": "supervisor-init", "label": "1. 初始化", "color": "2"}, {"id": "init-to-prompt", "fromNode": "supervisor-init", "toNode": "prompt-building", "label": "2. 构建提示", "color": "3"}, {"id": "input-to-memory", "fromNode": "user-input", "toNode": "chat-memory", "label": "3. 记忆构建", "color": "4"}, {"id": "memory-to-gpt", "fromNode": "chat-memory", "toNode": "gpt-analysis", "label": "4. GPT分析", "color": "5"}, {"id": "gpt-to-selection", "fromNode": "gpt-analysis", "toNode": "agent-selection", "label": "5. 智能体选择", "color": "6"}, {"id": "selection-to-routing", "fromNode": "agent-selection", "toNode": "routing-logic", "label": "6. 路由决策", "color": "7"}, {"id": "routing-to-invoke", "fromNode": "routing-logic", "toNode": "agent-invoke", "label": "7a. 成功路由", "color": "8"}, {"id": "routing-to-error", "fromNode": "routing-logic", "toNode": "error-handling", "label": "7b. 智能体未找到", "color": "9"}, {"id": "routing-to-clarify", "fromNode": "routing-logic", "toNode": "clarification", "label": "7c. 意图不明确", "color": "10"}, {"id": "selection-to-metadata", "fromNode": "agent-selection", "toNode": "metadata-structure", "label": "元数据参考", "color": "11"}]}