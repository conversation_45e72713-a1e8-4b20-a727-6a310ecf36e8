#Used to enable mocked class to take precedence over real class in unit tests
spring.main.lazy-initialization=true

openai.service=${AZURE_OPENAI_SERVICE}
openai.chatgpt.deployment=${AZURE_OPENAI_CHATGPT_DEPLOYMENT:gpt-4o}
openai.tracing.enabled=${AZURE_OPENAI_TRACING_ENABLED:false}

documentintelligence.service=${AZURE_DOCUMENT_INTELLIGENCE_SERVICE:example}


storage-account.service=${AZURE_STORAGE_ACCOUNT}
blob.container.name=${AZURE_STORAGE_CONTAINER:content}

logging.level.com.microsoft.openai.samples.rag.ask.approaches.semantickernel=DEBUG
logging.level.com.microsoft.semantickernel.samples.openapi.OpenAPIHttpRequestPlugin=DEBUG

server.error.include-message=always

# Support for User Assigned Managed identity
azure.identity.client-id=${AZURE_CLIENT_ID:system-managed-identity}

# MCP endpoints
transactions.api.url=${TRANSACTIONS_API_SERVER_URL}/sse
accounts.api.url=${ACCOUNTS_API_SERVER_URL}/sse
payments.api.url=${PAYMENTS_API_SERVER_URL}/sse