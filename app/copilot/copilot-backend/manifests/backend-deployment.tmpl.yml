apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-deployment
  namespace: azure-open-ai
  labels:
    app: backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
        - name: backend
          image: {{.Env.SERVICE_API_IMAGE_NAME}}
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8080
          envFrom:
            - configMapRef:
                name: azd-env-configmap
          resources:
            requests:
              memory: "2Gi"
