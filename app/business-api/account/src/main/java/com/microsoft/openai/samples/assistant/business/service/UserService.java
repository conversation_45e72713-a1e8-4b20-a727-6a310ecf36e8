package com.microsoft.openai.samples.assistant.business.service;

import com.microsoft.openai.samples.assistant.business.models.Account;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
public class UserService {


    private Map<String, Account> accounts = new HashMap<>();

    public UserService() {
        accounts.put(
                "<EMAIL>",
                new Account(
                        "1000",
                        "<EMAIL>",
                        "Alice User",
                        "USD",
                        "2022-01-01",
                        "5000",
                       null
                )
        );
        accounts.put(
                "<EMAIL>",
                new Account(
                        "1010",
                        "<EMAIL>",
                        "Bob User",
                        "EUR",
                        "2022-01-01",
                        "10000",
                       null
                )
        );
        accounts.put(
                "<EMAIL>",
                new Account(
                        "1020",
                        "<EMAIL>",
                        "Charlie User",
                        "EUR",
                        "2022-01-01",
                        "3000",
                        null
                )
        );

     }
    public List<Account> getAccountsByUserName(String userName) {
        return Arrays.asList(accounts.get(userName));
    }


}
