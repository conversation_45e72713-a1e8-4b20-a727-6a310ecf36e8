{"name": "frontend", "version": "1.0.0-alpha", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "frontend", "version": "1.0.0-alpha", "dependencies": {"@azure/msal-browser": "^3.1.0", "@azure/msal-react": "^2.0.4", "@fluentui/react": "^8.112.5", "@fluentui/react-components": "^9.37.3", "@fluentui/react-icons": "^2.0.221", "@react-spring/web": "^9.7.3", "dompurify": "^3.2.4", "frontend": "file:", "ndjson-readablestream": "^1.0.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "scheduler": "^0.20.2"}, "devDependencies": {"@types/dompurify": "^3.0.3", "@types/react": "^18.2.34", "@types/react-dom": "^18.2.14", "@vitejs/plugin-react": "^4.3.4", "prettier": "^3.0.3", "typescript": "^5.2.2", "vite": "^6.3.1"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@ampproject/remapping": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz", "integrity": "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==", "dev": true, "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@azure/msal-browser": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/@azure/msal-browser/-/msal-browser-3.2.0.tgz", "integrity": "sha512-le2qutddMiq0i3ErQaLKuwP1DpNgdd9iXPs3fSCsLuBrdGg9B4/j4ArCAHCwgxA82Ydj9BcqtMIL5BSWwU+P5A==", "dependencies": {"@azure/msal-common": "14.1.0"}, "engines": {"node": ">=0.8.0"}}, "node_modules/@azure/msal-common": {"version": "14.1.0", "resolved": "https://registry.npmjs.org/@azure/msal-common/-/msal-common-14.1.0.tgz", "integrity": "sha512-xphmhcfl5VL+uq5//VKMwQn+wfEZLMKNpFCcMi8Ur8ej5UT166g6chBsxgMzc9xo9Y24R9FB3m/tjDiV03xMIA==", "engines": {"node": ">=0.8.0"}}, "node_modules/@azure/msal-react": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/@azure/msal-react/-/msal-react-2.0.4.tgz", "integrity": "sha512-BujRm5FBDWYXyr3pnmubS4dIqZMlurYVtV2AyztoeAFUd+nh3XQZD9knHBqTyu53IDjhCCvUPUke/jSkv5WGlg==", "dependencies": {"@rollup/plugin-typescript": "^11.1.0", "rollup": "^3.20.2"}, "engines": {"node": ">=10"}, "peerDependencies": {"@azure/msal-browser": "^3.2.0", "react": "^16.8.0 || ^17 || ^18"}}, "node_modules/@babel/code-frame": {"version": "7.26.2", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.26.2.tgz", "integrity": "sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.25.9", "js-tokens": "^4.0.0", "picocolors": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.26.8", "resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.26.8.tgz", "integrity": "sha512-oH5UPLMWR3L2wEFLnFJ1TZXqHufiTKAiLfqw5zkhS4dKXLJ10yVztfil/twG8EDTA4F/tvVNw9nOl4ZMslB8rQ==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.26.9.tgz", "integrity": "sha512-lWBYIrF7qK5+GjY5Uy+/hEgp8OJWOD/rpy74GplYRhEauvbHDeFB8t5hPOZxCZ0Oxf4Cc36tK51/l3ymJysrKw==", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.26.2", "@babel/generator": "^7.26.9", "@babel/helper-compilation-targets": "^7.26.5", "@babel/helper-module-transforms": "^7.26.0", "@babel/helpers": "^7.26.9", "@babel/parser": "^7.26.9", "@babel/template": "^7.26.9", "@babel/traverse": "^7.26.9", "@babel/types": "^7.26.9", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/generator": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.26.9.tgz", "integrity": "sha512-kEWdzjOAUMW4hAyrzJ0ZaTOu9OmpyDIQicIh0zg0EEcEkYXZb2TjtBhnHi2ViX7PKwZqF4xwqfAm299/QMP3lg==", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.26.9", "@babel/types": "^7.26.9", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.26.5", "resolved": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.26.5.tgz", "integrity": "sha512-IXuyn5EkouFJscIDuFF5EsiSolseme1s0CZB+QxVugqJLYmKdxI1VfIBOst0SUu4rnk2Z7kqTwmoO1lp3HIfnA==", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.26.5", "@babel/helper-validator-option": "^7.25.9", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.25.9", "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.25.9.tgz", "integrity": "sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.26.0", "resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.26.0.tgz", "integrity": "sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/traverse": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.26.5", "resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz", "integrity": "sha512-RS+jZcRdZdRFzMyr+wcsaqOmld1/EqTghfaBGQQd/WnRdzdlvSZ//kF7U8VQTxf1ynZ4cjUcYgjVGx13ewNPMg==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.25.9", "resolved": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.25.9.tgz", "integrity": "sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.25.9", "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.9.tgz", "integrity": "sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.25.9", "resolved": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.25.9.tgz", "integrity": "sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.26.9.tgz", "integrity": "sha512-Mz/4+y8udxBKdmzt/UjPACs4G3j5SshJJEFFKxlCGPydG4JAHXxjWjAwjd09tf6oINvl1VfMJo+nB7H2YKQ0dA==", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.26.9", "@babel/types": "^7.26.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.26.9.tgz", "integrity": "sha512-81NWa1njQblgZbQHxWHpxxCzNsa3ZwvFqpUg7P+NNUU6f3UU2jBEg4OlF/J6rl8+PQGh1q6/zWScd001YwcA5A==", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.26.9"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-transform-react-jsx-self": {"version": "7.25.9", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.25.9.tgz", "integrity": "sha512-y8quW6p0WHkEhmErnfe58r7x0A70uKphQm8Sp8cV7tjNQwK56sNVK0M73LK3WuYmsuyrftut4xAkjjgU0twaMg==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-source": {"version": "7.25.9", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.25.9.tgz", "integrity": "sha512-+iqjT8xmXhhYv4/uiYd8FNQsraMFZIfxVSqxxVSZP0WbbSAWvBXAul0m/zu+7Vv4O/3WtApy9pmaTMiumEZgfg==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/runtime": {"version": "7.27.0", "resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.0.tgz", "integrity": "sha512-VtPOkrdPHZsKc/clNqyi9WUA8TINkZ4cGk63UUE3u4pmB2k+ZMQRDuIOagv8UVd6j7k0T3+RRIb7beKTebNbcw==", "license": "MIT", "dependencies": {"regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/template/-/template-7.26.9.tgz", "integrity": "sha512-qyRplbeIpNZhmzOysF/wFMuP9sctmh2cFzRAZOn1YapxBsE1i9bJIY586R/WBLfLcmcBlM8ROBiQURnnNy+zfA==", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.26.2", "@babel/parser": "^7.26.9", "@babel/types": "^7.26.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.26.9.tgz", "integrity": "sha512-ZYW7L+pL8ahU5fXmNbPF+iZFHCv5scFak7MZ9bwaRPLUhHh7QQEMjZUg0HevihoqCM5iSYHN61EyCoZvqC+bxg==", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.26.2", "@babel/generator": "^7.26.9", "@babel/parser": "^7.26.9", "@babel/template": "^7.26.9", "@babel/types": "^7.26.9", "debug": "^4.3.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.26.9.tgz", "integrity": "sha512-Y3IR1cRnOxOCDvMmNiym7XpXQ93iGDDPHx+Zj+NM+rg0fBaShfQLkg+hKPaZCEvg5N/LeCo4+Rj/i3FuJsIQaw==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@emotion/hash": {"version": "0.9.1", "resolved": "https://registry.npmjs.org/@emotion/hash/-/hash-0.9.1.tgz", "integrity": "sha512-gJB6HLm5rYwSLI6PQa+X1t5CFGrv1J1TWG+sOyMCeKz2ojaj6Fnl/rZEspogG+cvqbt4AE/2eIyD2QfLKTBNlQ=="}, "node_modules/@esbuild/aix-ppc64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/aix-ppc64/-/aix-ppc64-0.25.0.tgz", "integrity": "sha512-O7vun9Sf8DFjH2UtqK8Ku3LkquL9SZL8OLY1T5NZkA34+wG3OQF7cl4Ql8vdNzM6fzBbYfLaiRLIOZ+2FOCgBQ==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["aix"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-arm": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.25.0.tgz", "integrity": "sha512-PTyWCYYiU0+1eJKmw21lWtC+d08JDZPQ5g+kFyxP0V+es6VPPSUhM6zk8iImp2jbV6GwjX4pap0JFbUQN65X1g==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/android-arm64/-/android-arm64-0.25.0.tgz", "integrity": "sha512-grvv8WncGjDSyUBjN9yHXNt+cq0snxXbDxy5pJtzMKGmmpPxeAmAhWxXI+01lU5rwZomDgD3kJwulEnhTRUd6g==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/android-x64/-/android-x64-0.25.0.tgz", "integrity": "sha512-m/ix7SfKG5buCnxasr52+LI78SQ+wgdENi9CqyCXwjVR2X4Jkz+BpC3le3AoBPYTC9NHklwngVXvbJ9/Akhrfg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/darwin-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/darwin-arm64/-/darwin-arm64-0.25.0.tgz", "integrity": "sha512-mVwdUb5SRkPayVadIOI78K7aAnPamoeFR2bT5nszFUZ9P8UpK4ratOdYbZZXYSqPKMHfS1wdHCJk1P1EZpRdvw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/darwin-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/darwin-x64/-/darwin-x64-0.25.0.tgz", "integrity": "sha512-DgDaYsPWFTS4S3nWpFcMn/33ZZwAAeAFKNHNa1QN0rI4pUjgqf0f7ONmXf6d22tqTY+H9FNdgeaAa+YIFUn2Rg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/freebsd-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.0.tgz", "integrity": "sha512-VN4ocxy6dxefN1MepBx/iD1dH5K8qNtNe227I0mnTRjry8tj5MRk4zprLEdG8WPyAPb93/e4pSgi1SoHdgOa4w==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/freebsd-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.25.0.tgz", "integrity": "sha512-mrSgt7lCh07FY+hDD1TxiTyIHyttn6vnjesnPoVDNmDfOmggTLXRv8Id5fNZey1gl/V2dyVK1VXXqVsQIiAk+A==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-arm": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.25.0.tgz", "integrity": "sha512-vkB3IYj2IDo3g9xX7HqhPYxVkNQe8qTK55fraQyTzTX/fxaDtXiEnavv9geOsonh2Fd2RMB+i5cbhu2zMNWJwg==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-arm64/-/linux-arm64-0.25.0.tgz", "integrity": "sha512-9QAQjTWNDM/Vk2bgBl17yWuZxZNQIF0OUUuPZRKoDtqF2k4EtYbpyiG5/Dk7nqeK6kIJWPYldkOcBqjXjrUlmg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-ia32": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-ia32/-/linux-ia32-0.25.0.tgz", "integrity": "sha512-43ET5bHbphBegyeqLb7I1eYn2P/JYGNmzzdidq/w0T8E2SsYL1U6un2NFROFRg1JZLTzdCoRomg8Rvf9M6W6Gg==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-loong64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-loong64/-/linux-loong64-0.25.0.tgz", "integrity": "sha512-fC95c/xyNFueMhClxJmeRIj2yrSMdDfmqJnyOY4ZqsALkDrrKJfIg5NTMSzVBr5YW1jf+l7/cndBfP3MSDpoHw==", "cpu": ["loong64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-mips64el": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.25.0.tgz", "integrity": "sha512-nkAMFju7KDW73T1DdH7glcyIptm95a7Le8irTQNO/qtkoyypZAnjchQgooFUDQhNAy4iu08N79W4T4pMBwhPwQ==", "cpu": ["mips64el"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-ppc64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-ppc64/-/linux-ppc64-0.25.0.tgz", "integrity": "sha512-NhyOejdhRGS8Iwv+KKR2zTq2PpysF9XqY+Zk77vQHqNbo/PwZCzB5/h7VGuREZm1fixhs4Q/qWRSi5zmAiO4Fw==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-riscv64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-riscv64/-/linux-riscv64-0.25.0.tgz", "integrity": "sha512-5S/rbP5OY+GHLC5qXp1y/Mx//e92L1YDqkiBbO9TQOvuFXM+iDqUNG5XopAnXoRH3FjIUDkeGcY1cgNvnXp/kA==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-s390x": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.25.0.tgz", "integrity": "sha512-XM2BFsEBz0Fw37V0zU4CXfcfuACMrppsMFKdYY2WuTS3yi8O1nFOhil/xhKTmE1nPmVyvQJjJivgDT+xh8pXJA==", "cpu": ["s390x"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-x64/-/linux-x64-0.25.0.tgz", "integrity": "sha512-9yl91rHw/cpwMCNytUDxwj2XjFpxML0y9HAOH9pNVQDpQrBxHy01Dx+vaMu0N1CKa/RzBD2hB4u//nfc+Sd3Cw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/netbsd-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.0.tgz", "integrity": "sha512-RuG4PSMPFfrkH6UwCAqBzauBWTygTvb1nxWasEJooGSJ/NwRw7b2HOwyRTQIU97Hq37l3npXoZGYMy3b3xYvPw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/netbsd-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.25.0.tgz", "integrity": "sha512-jl+qisSB5jk01N5f7sPCsBENCOlPiS/xptD5yxOx2oqQfyourJwIKLRA2yqWdifj3owQZCL2sn6o08dBzZGQzA==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openbsd-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.0.tgz", "integrity": "sha512-21sUNbq2r84YE+SJDfaQRvdgznTD8Xc0oc3p3iW/a1EVWeNj/SdUCbm5U0itZPQYRuRTW20fPMWMpcrciH2EJw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openbsd-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/openbsd-x64/-/openbsd-x64-0.25.0.tgz", "integrity": "sha512-2gwwriSMPcCFRlPlKx3zLQhfN/2WjJ2NSlg5TKLQOJdV0mSxIcYNTMhk3H3ulL/cak+Xj0lY1Ym9ysDV1igceg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/sunos-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.25.0.tgz", "integrity": "sha512-bxI7ThgLzPrPz484/S9jLlvUAHYMzy6I0XiU1ZMeAEOBcS0VePBFxh1JjTQt3Xiat5b6Oh4x7UC7IwKQKIJRIg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["sunos"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.25.0.tgz", "integrity": "sha512-ZUAc2YK6JW89xTbXvftxdnYy3m4iHIkDtK3CLce8wg8M2L+YZhIvO1DKpxrd0Yr59AeNNkTiic9YLf6FTtXWMw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-ia32": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/win32-ia32/-/win32-ia32-0.25.0.tgz", "integrity": "sha512-eSNxISBu8XweVEWG31/JzjkIGbGIJN/TrRoiSVZwZ6pkC6VX4Im/WV2cz559/TXLcYbcrDN8JtKgd9DJVIo8GA==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.25.0.tgz", "integrity": "sha512-ZENoHJBxA20C2zFzh6AI4fT6RraMzjYw4xKWemRTRmRVtN9c5DcH9r/f2ihEkMjOW5eGgrwCslG/+Y/3bL+DHQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@floating-ui/core": {"version": "1.5.3", "resolved": "https://registry.npmjs.org/@floating-ui/core/-/core-1.5.3.tgz", "integrity": "sha512-O0WKDOo0yhJuugCx6trZQj5jVJ9yR0ystG2JaNAemYUWce+pmM6WUEFIibnWyEJKdrDxhm75NoSRME35FNaM/Q==", "dependencies": {"@floating-ui/utils": "^0.2.0"}}, "node_modules/@floating-ui/devtools": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@floating-ui/devtools/-/devtools-0.2.1.tgz", "integrity": "sha512-8PHJLbD6VhBh+LJ1uty/Bz30qs02NXCE5u8WpOhSewlYXUWl03GNXknr9AS2yaAWJEQaY27x7eByJs44gODBcw==", "peerDependencies": {"@floating-ui/dom": ">=1.5.4"}}, "node_modules/@floating-ui/dom": {"version": "1.5.4", "resolved": "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.5.4.tgz", "integrity": "sha512-jByEsHIY+eEdCjnTVu+E3ephzTOzkQ8hgUfGwos+bg7NlH33Zc5uO+QHz1mrQUOgIKKDD1RtS201P9NvAfq3XQ==", "dependencies": {"@floating-ui/core": "^1.5.3", "@floating-ui/utils": "^0.2.0"}}, "node_modules/@floating-ui/utils": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.1.tgz", "integrity": "sha512-9TANp6GPoMtYzQdt54kfAyMmz1+osLlXdg2ENroU7zzrtflTLrrC/lgrIfaSe+Wu0b89GKccT7vxXA0MoAIO+Q=="}, "node_modules/@fluentui/date-time-utilities": {"version": "8.5.16", "resolved": "https://registry.npmjs.org/@fluentui/date-time-utilities/-/date-time-utilities-8.5.16.tgz", "integrity": "sha512-l+mLfJ2VhdHjBpELLLPDaWgT7GMLynm2aqR7SttbEb6Jh7hc/7ck1MWm93RTb3gYVHYai8SENqimNcvIxHt/zg==", "dependencies": {"@fluentui/set-version": "^8.2.14", "tslib": "^2.1.0"}}, "node_modules/@fluentui/dom-utilities": {"version": "2.2.14", "resolved": "https://registry.npmjs.org/@fluentui/dom-utilities/-/dom-utilities-2.2.14.tgz", "integrity": "sha512-+4DVm5sNfJh+l8fM+7ylpOkGNZkNr4X1z1uKQPzRJ1PRhlnvc6vLpWNNicGwpjTbgufSrVtGKXwP5sf++r81lg==", "dependencies": {"@fluentui/set-version": "^8.2.14", "tslib": "^2.1.0"}}, "node_modules/@fluentui/font-icons-mdl2": {"version": "8.5.31", "resolved": "https://registry.npmjs.org/@fluentui/font-icons-mdl2/-/font-icons-mdl2-8.5.31.tgz", "integrity": "sha512-jioHZ9XUfR9vUT5XnxdCrJ+hoC9TpYim+4YdtlUE/euI8kdW1tDZ5zqlSNk1GLDR34n03R09yWj5gVDCcMJbyQ==", "dependencies": {"@fluentui/set-version": "^8.2.14", "@fluentui/style-utilities": "^8.10.2", "@fluentui/utilities": "^8.13.24", "tslib": "^2.1.0"}}, "node_modules/@fluentui/foundation-legacy": {"version": "8.2.51", "resolved": "https://registry.npmjs.org/@fluentui/foundation-legacy/-/foundation-legacy-8.2.51.tgz", "integrity": "sha512-z/jrp1imV66/D2MGpN/55LGk/Istymk5tN+XUFHDENDi+9zyb2MgSxFshp774DJIrg3vVlyuS8oo+dBuTM3UbQ==", "dependencies": {"@fluentui/merge-styles": "^8.5.15", "@fluentui/set-version": "^8.2.14", "@fluentui/style-utilities": "^8.10.2", "@fluentui/utilities": "^8.13.24", "tslib": "^2.1.0"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "react": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/keyboard-key": {"version": "0.4.14", "resolved": "https://registry.npmjs.org/@fluentui/keyboard-key/-/keyboard-key-0.4.14.tgz", "integrity": "sha512-XzZHcyFEM20H23h3i15UpkHi2AhRBriXPGAHq0Jm98TKFppXehedjjEFuUsh+CyU5JKBhDalWp8TAQ1ArpNzow==", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/@fluentui/keyboard-keys": {"version": "9.0.7", "resolved": "https://registry.npmjs.org/@fluentui/keyboard-keys/-/keyboard-keys-9.0.7.tgz", "integrity": "sha512-vaQ+lOveQTdoXJYqDQXWb30udSfTVcIuKk1rV0X0eGAgcHeSDeP1HxMy+OgHOQZH3OiBH4ZYeWxb+tmfiDiygQ==", "dependencies": {"@swc/helpers": "^0.5.1"}}, "node_modules/@fluentui/merge-styles": {"version": "8.5.15", "resolved": "https://registry.npmjs.org/@fluentui/merge-styles/-/merge-styles-8.5.15.tgz", "integrity": "sha512-4CdKwo4k1Un2QLulpSVIz/KMgLNBMgin4NPyapmKDMVuO1OOxJUqfocubRGNO5x9mKgAMMYwBKGO9i0uxMMpJw==", "dependencies": {"@fluentui/set-version": "^8.2.14", "tslib": "^2.1.0"}}, "node_modules/@fluentui/priority-overflow": {"version": "9.1.11", "resolved": "https://registry.npmjs.org/@fluentui/priority-overflow/-/priority-overflow-9.1.11.tgz", "integrity": "sha512-sdrpavvKX2kepQ1d6IaI3ObLq5SAQBPRHPGx2+wiMWL7cEx9vGGM0fmeicl3soqqmM5uwCmWnZk9QZv9XOY98w==", "dependencies": {"@swc/helpers": "^0.5.1"}}, "node_modules/@fluentui/react": {"version": "8.114.4", "resolved": "https://registry.npmjs.org/@fluentui/react/-/react-8.114.4.tgz", "integrity": "sha512-dVpfFSpWUxdyqWlCVSXX5d34S760h4MaQjGR2/TPavtcJRRpJDHbBN2Hn7s4riA6YX5N7bTdN372UvIVbBbzuw==", "dependencies": {"@fluentui/date-time-utilities": "^8.5.16", "@fluentui/font-icons-mdl2": "^8.5.31", "@fluentui/foundation-legacy": "^8.2.51", "@fluentui/merge-styles": "^8.5.15", "@fluentui/react-focus": "^8.8.38", "@fluentui/react-hooks": "^8.6.36", "@fluentui/react-portal-compat-context": "^9.0.11", "@fluentui/react-window-provider": "^2.2.18", "@fluentui/set-version": "^8.2.14", "@fluentui/style-utilities": "^8.10.2", "@fluentui/theme": "^2.6.41", "@fluentui/utilities": "^8.13.24", "@microsoft/load-themed-styles": "^1.10.26", "tslib": "^2.1.0"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.8.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-accordion": {"version": "9.3.38", "resolved": "https://registry.npmjs.org/@fluentui/react-accordion/-/react-accordion-9.3.38.tgz", "integrity": "sha512-BB8d9+Jr0v4SW58OJTIyvsxhA/iOBbvIkQZlVHKqt4tL8dHOIFPrApw5WqQqaSYJsEwt4HxmlNU4Dv8qRughbg==", "dependencies": {"@fluentui/react-aria": "^9.7.3", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": "^0.19.0 || ^0.20.0"}}, "node_modules/@fluentui/react-alert": {"version": "9.0.0-beta.104", "resolved": "https://registry.npmjs.org/@fluentui/react-alert/-/react-alert-9.0.0-beta.104.tgz", "integrity": "sha512-Z8BGSyzEKok5wlJF2cUc8GUj2q+c1D+119YF0WtHLiieh7pwOHjBcDJOHqnaVnQNbhetIA3NUht2z0e1wgOK5w==", "dependencies": {"@fluentui/react-avatar": "^9.6.9", "@fluentui/react-button": "^9.3.65", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-aria": {"version": "9.7.3", "resolved": "https://registry.npmjs.org/@fluentui/react-aria/-/react-aria-9.7.3.tgz", "integrity": "sha512-YwyPNEcBDCdY6YzhrIrtlSrLs2Le7X1jLq9em8OnqHeiO22dBmg5xlBJoAMwJ8awCpI9xhu1PhU/2VJY4YqNuA==", "dependencies": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-utilities": "^9.16.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-avatar": {"version": "9.6.9", "resolved": "https://registry.npmjs.org/@fluentui/react-avatar/-/react-avatar-9.6.9.tgz", "integrity": "sha512-3aZeUhGOg+UlHsp2x//G4VKRWKclcsZvX6L9UVnHsA/nQqRw7C5Bfo9iFNsEeJ3R5W5mFA6LyEFWedJ7QdAmdQ==", "dependencies": {"@fluentui/react-badge": "^9.2.22", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-popover": "^9.8.33", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-tooltip": "^9.4.11", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": "^0.19.0 || ^0.20.0"}}, "node_modules/@fluentui/react-badge": {"version": "9.2.22", "resolved": "https://registry.npmjs.org/@fluentui/react-badge/-/react-badge-9.2.22.tgz", "integrity": "sha512-zzimP5mZiiCOm8expUTzD6yvvKbnKq22PK/L6+oNpifrvQnDwJF/0nwXQVjA3+icNoYTaHe/q0fFivpXV+Js6g==", "dependencies": {"@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-breadcrumb": {"version": "9.0.11", "resolved": "https://registry.npmjs.org/@fluentui/react-breadcrumb/-/react-breadcrumb-9.0.11.tgz", "integrity": "sha512-L+AQqZz1gqkScD8IW1CjZWGNrDaHDc/gSv+PrvgSZeGDPibGj6TnLygJ7BKM+rQ+Hc2SbCogKbERpQZCbrSFvA==", "dependencies": {"@fluentui/react-aria": "^9.7.3", "@fluentui/react-button": "^9.3.65", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-link": "^9.2.7", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-button": {"version": "9.3.65", "resolved": "https://registry.npmjs.org/@fluentui/react-button/-/react-button-9.3.65.tgz", "integrity": "sha512-3VOt29AugkfR7VMnkKON449E7Sn/nvc6BBT4kJDGKQY+Nm5d2p9e4HmHp1UaM9zRPt47lagTY2WFJNrKKSe/BA==", "dependencies": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-card": {"version": "9.0.64", "resolved": "https://registry.npmjs.org/@fluentui/react-card/-/react-card-9.0.64.tgz", "integrity": "sha512-TB/Zk+tLDUPNyAd2y8BvN0T2nroimtBOpB5GTK72E5sWPk0kaKIHwBEfXxNFGdGXcw0TAmVNqYi4ks37vh0Rgg==", "dependencies": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-checkbox": {"version": "9.2.8", "resolved": "https://registry.npmjs.org/@fluentui/react-checkbox/-/react-checkbox-9.2.8.tgz", "integrity": "sha512-L4aWzeZdi98d0ZhgNPtxghfhasQv1qlxIRMaPxtwvk5TN6i9YmRF8vf5Pmf0PESjT+zp3VPcisHcIfcqG26SmQ==", "dependencies": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-combobox": {"version": "9.7.0", "resolved": "https://registry.npmjs.org/@fluentui/react-combobox/-/react-combobox-9.7.0.tgz", "integrity": "sha512-YmTdg04rvsg2+Dkw3ob+YLnS9rm3TLVMMNYTH0T64/FM3qirHntIXGbhMZXP5Cdo14gzQwr/e78NjBRKfYO4Wg==", "dependencies": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-field": "^9.1.50", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-positioning": "^9.12.4", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": "^0.19.0 || ^0.20.0"}}, "node_modules/@fluentui/react-components": {"version": "9.45.0", "resolved": "https://registry.npmjs.org/@fluentui/react-components/-/react-components-9.45.0.tgz", "integrity": "sha512-Y+Laj1dvRcCp/nWT0DExRXoh7oKTX458g6oltrGjhIHikq4D6/kssK5tfhCyknPLwIlVSYi5J+G6L3NfvI8a8w==", "dependencies": {"@fluentui/react-accordion": "^9.3.38", "@fluentui/react-alert": "9.0.0-beta.104", "@fluentui/react-avatar": "^9.6.9", "@fluentui/react-badge": "^9.2.22", "@fluentui/react-breadcrumb": "^9.0.11", "@fluentui/react-button": "^9.3.65", "@fluentui/react-card": "^9.0.64", "@fluentui/react-checkbox": "^9.2.8", "@fluentui/react-combobox": "^9.7.0", "@fluentui/react-dialog": "^9.9.7", "@fluentui/react-divider": "^9.2.58", "@fluentui/react-drawer": "^9.1.1", "@fluentui/react-field": "^9.1.50", "@fluentui/react-image": "^9.1.55", "@fluentui/react-infobutton": "9.0.0-beta.88", "@fluentui/react-infolabel": "^9.0.16", "@fluentui/react-input": "^9.4.60", "@fluentui/react-label": "^9.1.58", "@fluentui/react-link": "^9.2.7", "@fluentui/react-menu": "^9.12.45", "@fluentui/react-message-bar": "^9.0.16", "@fluentui/react-overflow": "^9.1.8", "@fluentui/react-persona": "^9.2.68", "@fluentui/react-popover": "^9.8.33", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-positioning": "^9.12.4", "@fluentui/react-progress": "^9.1.60", "@fluentui/react-provider": "^9.13.8", "@fluentui/react-radio": "^9.2.3", "@fluentui/react-select": "^9.1.60", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-skeleton": "^9.0.48", "@fluentui/react-slider": "^9.1.65", "@fluentui/react-spinbutton": "^9.2.60", "@fluentui/react-spinner": "^9.3.38", "@fluentui/react-switch": "^9.1.65", "@fluentui/react-table": "^9.11.5", "@fluentui/react-tabs": "^9.4.6", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-tags": "^9.0.22", "@fluentui/react-text": "^9.4.7", "@fluentui/react-textarea": "^9.3.60", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-toast": "^9.3.27", "@fluentui/react-toolbar": "^9.1.66", "@fluentui/react-tooltip": "^9.4.11", "@fluentui/react-tree": "^9.4.25", "@fluentui/react-utilities": "^9.16.1", "@fluentui/react-virtualizer": "9.0.0-alpha.66", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": "^0.19.0 || ^0.20.0"}}, "node_modules/@fluentui/react-context-selector": {"version": "9.1.49", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.1.49.tgz", "integrity": "sha512-u4wRNfnyfuZDalVEESBPFQ0Ue4yYu+ozkPQvuEV6kriQGnAQQyyVbIidOCuP7Sja0nBwgM8eAzK0uX/slmmj3Q==", "dependencies": {"@fluentui/react-utilities": "^9.16.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": "^0.19.0 || ^0.20.0"}}, "node_modules/@fluentui/react-dialog": {"version": "9.9.7", "resolved": "https://registry.npmjs.org/@fluentui/react-dialog/-/react-dialog-9.9.7.tgz", "integrity": "sha512-5/6MeaHOYpx8Vt0auMJGLCjn6O1IYtl6IhwdwRNXL6AS1o4F24IKXdWZPtiHWuvzbuZAQd3+5nRDUE5KC9We6A==", "dependencies": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1", "react-transition-group": "^4.4.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-divider": {"version": "9.2.58", "resolved": "https://registry.npmjs.org/@fluentui/react-divider/-/react-divider-9.2.58.tgz", "integrity": "sha512-y1ECy1zM4imKhpyOyUGugB+J30tfySO5hhrsIcpaiUQxRjE4IhZf2ZG6EqAQYLinJ+hV06yLZoazekljlvk6yw==", "dependencies": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-drawer": {"version": "9.1.1", "resolved": "https://registry.npmjs.org/@fluentui/react-drawer/-/react-drawer-9.1.1.tgz", "integrity": "sha512-3zvbbeaLLJZa4MXRpW8Ta4DFZ5457Tq9/4a0CqsIW/+8EuwtJwO+FB5a0DS6j0q6kN4mjkWF19OvzMkJsSTRVw==", "dependencies": {"@fluentui/react-dialog": "^9.9.7", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-motion-preview": "^0.5.10", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-field": {"version": "9.1.50", "resolved": "https://registry.npmjs.org/@fluentui/react-field/-/react-field-9.1.50.tgz", "integrity": "sha512-2mbx7YReMWvrgi3set9KepLLgMyNJ7StLu/HiHMM3jkcgPt3mGfwoJEsEKt+xd8eUAo4b82F7t+tHI4f9yzJaQ==", "dependencies": {"@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-focus": {"version": "8.8.38", "resolved": "https://registry.npmjs.org/@fluentui/react-focus/-/react-focus-8.8.38.tgz", "integrity": "sha512-vnsaY7hJSPIJBxm5Pj0FrcFDumV6kKgFVpsKsEKJzb1D88rDDLcmvz9jWUx68a3ru6idEbZYmyePGT1IiRsAug==", "dependencies": {"@fluentui/keyboard-key": "^0.4.14", "@fluentui/merge-styles": "^8.5.15", "@fluentui/set-version": "^8.2.14", "@fluentui/style-utilities": "^8.10.2", "@fluentui/utilities": "^8.13.24", "tslib": "^2.1.0"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "react": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-hooks": {"version": "8.6.36", "resolved": "https://registry.npmjs.org/@fluentui/react-hooks/-/react-hooks-8.6.36.tgz", "integrity": "sha512-kI0Z4Q4xHUs4SOmmI5n5OH5fPckqMSCovTRpiuxzCO2TNzLmfC861+nqf4Ygw/ChqNm2gWNZZfUADfnNAEsq+Q==", "dependencies": {"@fluentui/react-window-provider": "^2.2.18", "@fluentui/set-version": "^8.2.14", "@fluentui/utilities": "^8.13.24", "tslib": "^2.1.0"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "react": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-icons": {"version": "2.0.225", "resolved": "https://registry.npmjs.org/@fluentui/react-icons/-/react-icons-2.0.225.tgz", "integrity": "sha512-L9phN3bAMlZCa5+/ObGjIO+5GI8M50ym766sraSq92jaJwgAXrCJDLWuDGWZRGrC63DcagtR2culptj3q7gMMg==", "dependencies": {"@griffel/react": "^1.0.0", "tslib": "^2.1.0"}, "peerDependencies": {"react": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-image": {"version": "9.1.55", "resolved": "https://registry.npmjs.org/@fluentui/react-image/-/react-image-9.1.55.tgz", "integrity": "sha512-hYP61OWLuGSJNPOGJXtphbiDESfLB+/vsODKQsJhrDRJ2CSNMAfNznPHucqGRRN6AWQOI/BynJDS5F22Y//7CQ==", "dependencies": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-infobutton": {"version": "9.0.0-beta.88", "resolved": "https://registry.npmjs.org/@fluentui/react-infobutton/-/react-infobutton-9.0.0-beta.88.tgz", "integrity": "sha512-NVZyfrLtoFNu7cGkp2ORWsxJiCk1JgN4CVBDj03QSIh14EsPMwphYgDwfQ8TZOF2Nub0DGtC7/tF8IUlb/aP6g==", "dependencies": {"@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-popover": "^9.8.33", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-infolabel": {"version": "9.0.16", "resolved": "https://registry.npmjs.org/@fluentui/react-infolabel/-/react-infolabel-9.0.16.tgz", "integrity": "sha512-UCY+2vB4vOn0LfVhbgkyNG0EiuKIe0PdxEAtLU2PqosHLkaLKnYDKJdiIS/oaFmyNtGHmMxRkigvZpZ7h74f9g==", "dependencies": {"@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-popover": "^9.8.33", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.8.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-input": {"version": "9.4.60", "resolved": "https://registry.npmjs.org/@fluentui/react-input/-/react-input-9.4.60.tgz", "integrity": "sha512-kuk24K0X0gckTCssXoiWvZsTFVpZJv+WPl2fkjxeffzmFfBZtJUFQkXeC4/hcAg+aScjZnEtqjHjwDEbjZqkeA==", "dependencies": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-jsx-runtime": {"version": "9.0.27", "resolved": "https://registry.npmjs.org/@fluentui/react-jsx-runtime/-/react-jsx-runtime-9.0.27.tgz", "integrity": "sha512-9wxsWxVI7RLXsdK+7lzp7TK0FJKnrrj+Igxn0prqAvXdBRiFcuycoCJaHzC4Ka+Hsiol8NQg6xaIR59a28lmyQ==", "dependencies": {"@fluentui/react-utilities": "^9.16.1", "@swc/helpers": "^0.5.1", "react-is": "^17.0.2"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "react": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-label": {"version": "9.1.58", "resolved": "https://registry.npmjs.org/@fluentui/react-label/-/react-label-9.1.58.tgz", "integrity": "sha512-0ouSMop4vpXJzMvAyfmIr3TgDM/W1k+GFm8ZPD5fDQCopSJ+h3kvUZg5pqaXpBwamvZ16+qRARfTNITp2U7Rjw==", "dependencies": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-link": {"version": "9.2.7", "resolved": "https://registry.npmjs.org/@fluentui/react-link/-/react-link-9.2.7.tgz", "integrity": "sha512-z4X9dcUc/7FlqDxbGKbOfWubru+QimtzgMtlVxZ30pkC959hfIbFpbBY6Me76UOuFiOZxUPdfyY/73ekhhhVxw==", "dependencies": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-menu": {"version": "9.12.45", "resolved": "https://registry.npmjs.org/@fluentui/react-menu/-/react-menu-9.12.45.tgz", "integrity": "sha512-qhpmuvAB4DUmmC5lNMakVvZjTdj/GZnH6WctNGZp94iCZLhcnIQcM9l0PvRpUpU1v3irXRyE5QV+x+wXC0awTw==", "dependencies": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-positioning": "^9.12.4", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": "^0.19.0 || ^0.20.0"}}, "node_modules/@fluentui/react-message-bar": {"version": "9.0.16", "resolved": "https://registry.npmjs.org/@fluentui/react-message-bar/-/react-message-bar-9.0.16.tgz", "integrity": "sha512-R1VnqcFwu0pM2Yk8rjkN48Lx/n44UFD13BuY8/JeEuU8XQ8hLnEBVtdHjzRPJk+iM5in2ScMMQj4Z0nWyCRM1Q==", "dependencies": {"@fluentui/react-button": "^9.3.65", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1", "react-transition-group": "^4.4.1"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.8.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-motion-preview": {"version": "0.5.10", "resolved": "https://registry.npmjs.org/@fluentui/react-motion-preview/-/react-motion-preview-0.5.10.tgz", "integrity": "sha512-6iwF3N4hB6IxCoFVusgA2mp6mrTknwcsVGNYEQw1YF5WgGOMF3M0N1xNpN61/SYziT6HSUaI38NaA7LI3Dp3Sw==", "dependencies": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-overflow": {"version": "9.1.8", "resolved": "https://registry.npmjs.org/@fluentui/react-overflow/-/react-overflow-9.1.8.tgz", "integrity": "sha512-W8L68+0bUtfGr72LRx+U05EZLO0E8VMfscDiNKiEjDrOqdBnqNAIDN86825wrN77HH2wvILN07EhPOauqzz8YQ==", "dependencies": {"@fluentui/priority-overflow": "^9.1.11", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": "^0.19.0 || ^0.20.0"}}, "node_modules/@fluentui/react-persona": {"version": "9.2.68", "resolved": "https://registry.npmjs.org/@fluentui/react-persona/-/react-persona-9.2.68.tgz", "integrity": "sha512-C<PERSON>tDiZ34GGaw7lZ85uHZOuYXzkY21VHN6cUlGY1TJn98+Xz+y7JoVLIG7KZHHp2JzmmjtwjvgnqAdOun5LrWig==", "dependencies": {"@fluentui/react-avatar": "^9.6.9", "@fluentui/react-badge": "^9.2.22", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-popover": {"version": "9.8.33", "resolved": "https://registry.npmjs.org/@fluentui/react-popover/-/react-popover-9.8.33.tgz", "integrity": "sha512-0yPX6KCdMEGmrvJnQles5iTKN0OZ2vNSPVdkbyEKIUKj5DrNK1cMZEV/7Tgrtn922fx3/74FLMqEpEDTdrvQ/Q==", "dependencies": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-positioning": "^9.12.4", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": "^0.19.0 || ^0.20.0"}}, "node_modules/@fluentui/react-portal": {"version": "9.4.10", "resolved": "https://registry.npmjs.org/@fluentui/react-portal/-/react-portal-9.4.10.tgz", "integrity": "sha512-k8fTRv9wTPSPCuNBFE2HxIhXsVYoG6Azb6Ib2xaDK+nczoW2WbsmNmwBJGEGi8UKjIoQzV+95KsYQ9me+uqKPA==", "dependencies": {"@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1", "use-disposable": "^1.0.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-portal-compat-context": {"version": "9.0.11", "resolved": "https://registry.npmjs.org/@fluentui/react-portal-compat-context/-/react-portal-compat-context-9.0.11.tgz", "integrity": "sha512-ubvW/ej0O+Pago9GH3mPaxzUgsNnBoqvghNamWjyKvZIViyaXUG6+sgcAl721R+qGAFac+A20akI5qDJz/xtdg==", "dependencies": {"@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "react": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-positioning": {"version": "9.12.4", "resolved": "https://registry.npmjs.org/@fluentui/react-positioning/-/react-positioning-9.12.4.tgz", "integrity": "sha512-qQAjHF/FJFs2TyK0x08t0iFtDQlGNGH0OFC3jrG1xIFEe3nFPoeYeNT3zxOmj+D7bvlcJTIITcoe++YQTnCf4w==", "dependencies": {"@floating-ui/devtools": "0.2.1", "@floating-ui/dom": "^1.2.0", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-progress": {"version": "9.1.60", "resolved": "https://registry.npmjs.org/@fluentui/react-progress/-/react-progress-9.1.60.tgz", "integrity": "sha512-9wC7lWdo3S8rhxKWlIhcYAzsZNw+rL2HvNJTvEvFxXcOG7nJxP/3mGclV/jCCwDoPDnt9BT+40pGK84eD0BNIA==", "dependencies": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-provider": {"version": "9.13.8", "resolved": "https://registry.npmjs.org/@fluentui/react-provider/-/react-provider-9.13.8.tgz", "integrity": "sha512-FCvDMjs/BNAcqJuHU+kN/lqLB2RDQ/LQo29ltfLKFlTR1nTUNJvPMOVhjj6eEt+t81628LOYhbbaXOj9rYtfGg==", "dependencies": {"@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/core": "^1.14.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-radio": {"version": "9.2.3", "resolved": "https://registry.npmjs.org/@fluentui/react-radio/-/react-radio-9.2.3.tgz", "integrity": "sha512-8eKeUL0ZNr792Q6NGWPp7dpOV2IFcjAQ2oWR2/bruQVu8LMzYYKe2o6pQWdCag6UGPZuszkms9Xl7zPdDQBUdA==", "dependencies": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": "^0.19.0 || ^0.20.0"}}, "node_modules/@fluentui/react-select": {"version": "9.1.60", "resolved": "https://registry.npmjs.org/@fluentui/react-select/-/react-select-9.1.60.tgz", "integrity": "sha512-4HfRRTlGStOgtO00RY6jmOwz6MXnoa9gtjkV7StLmJZ2U5NTjVUrnp2dP1Vjb6hO13xaihWGEYyYKnsQ3R7kIw==", "dependencies": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-shared-contexts": {"version": "9.14.0", "resolved": "https://registry.npmjs.org/@fluentui/react-shared-contexts/-/react-shared-contexts-9.14.0.tgz", "integrity": "sha512-P9yhg31WYfB1W66/gD3+qVCLBsyIEcOzQvKVaIQvd9UhF67lNW4kMXUB6YVOk5PV0Og4hXnkH/vuHl7YMD9RHw==", "dependencies": {"@fluentui/react-theme": "^9.1.16", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "react": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-skeleton": {"version": "9.0.48", "resolved": "https://registry.npmjs.org/@fluentui/react-skeleton/-/react-skeleton-9.0.48.tgz", "integrity": "sha512-P0Rw5hIOn5CrZIWg7nVoK3gamxFhZI80KcRVaWap4O3gLo5C8nKHJWOtyBQZ5WKH+S6hoEGZ2USL6CoyXslxeQ==", "dependencies": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-slider": {"version": "9.1.65", "resolved": "https://registry.npmjs.org/@fluentui/react-slider/-/react-slider-9.1.65.tgz", "integrity": "sha512-7kuJMIojxCmNOuiRmQwh9iiXx8zwxkrgvsWmReRIBX0WB6w1VqMcuuikq2Z2ISgNPmepCX8W+qDfx8Ne4F/HtQ==", "dependencies": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-spinbutton": {"version": "9.2.60", "resolved": "https://registry.npmjs.org/@fluentui/react-spinbutton/-/react-spinbutton-9.2.60.tgz", "integrity": "sha512-0IIxEH0CTf4fNMoyvMa37bc63+0ZlznlsNy8lF3hujAT8Z9sUKVMH68e6tGUuXGJIkCUyDKU8HA+9FF2DyPvNA==", "dependencies": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-field": "^9.1.50", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-spinner": {"version": "9.3.38", "resolved": "https://registry.npmjs.org/@fluentui/react-spinner/-/react-spinner-9.3.38.tgz", "integrity": "sha512-dPJr7/rgU2Qe/K2BciJTAEwEd0ytGpCw3VOVyK2T25w7Jw5RAHmgP+mbw+7se44Mr6sd1LH76mh5sfmQ3tODgw==", "dependencies": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-switch": {"version": "9.1.65", "resolved": "https://registry.npmjs.org/@fluentui/react-switch/-/react-switch-9.1.65.tgz", "integrity": "sha512-P0DwogD6hZJ3O005zCFPDoFXuzkrpKMrAeQGh9X0fqFP5JyHXVCgAAZQOLcphbbT9QukoEF5irN2Z4L9gBn57A==", "dependencies": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-table": {"version": "9.11.5", "resolved": "https://registry.npmjs.org/@fluentui/react-table/-/react-table-9.11.5.tgz", "integrity": "sha512-roQ<PERSON><PERSON>tl1aqXlachS2oTraVE45x3KdDrX0KyQGCdcQRxNprXJW6dIK9QjlbAL6yAsAMDafmFA4y9uRxl408dQ==", "dependencies": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-avatar": "^9.6.9", "@fluentui/react-checkbox": "^9.2.8", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-radio": "^9.2.3", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-tabs": {"version": "9.4.6", "resolved": "https://registry.npmjs.org/@fluentui/react-tabs/-/react-tabs-9.4.6.tgz", "integrity": "sha512-LQvibLeJFyqKKiOjZUkRvbfLtsVosUhNUdh1SCQUPxQVpEPSK6XgwK0A1+jjoVhKn+PAJakxRINgnvqQD8pQBA==", "dependencies": {"@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": "^0.19.0 || ^0.20.0"}}, "node_modules/@fluentui/react-tabster": {"version": "9.17.3", "resolved": "https://registry.npmjs.org/@fluentui/react-tabster/-/react-tabster-9.17.3.tgz", "integrity": "sha512-cFcUYrkGW15w5yXzCPTTVG/7x5kNXxnhQXuh8SPyCc9JZeG7XI3+hy1T37PsXGxNS4KN9ePHkBHzgDfYO4gzYQ==", "dependencies": {"@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1", "keyborg": "^2.3.0", "tabster": "^5.0.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-tags": {"version": "9.0.22", "resolved": "https://registry.npmjs.org/@fluentui/react-tags/-/react-tags-9.0.22.tgz", "integrity": "sha512-gQIOCVu3HIfGjtAmwOnwBEnTsNyRBU8Pvs6EugpUyyqkRjzbm5TnL3LtiUy4f6/+NuaRqcYAvhwpdUhrlciwcA==", "dependencies": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-avatar": "^9.6.9", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-text": {"version": "9.4.7", "resolved": "https://registry.npmjs.org/@fluentui/react-text/-/react-text-9.4.7.tgz", "integrity": "sha512-c6uJ98B35L8sviYxhQj1i+LW+HVNDdco2ImS9VLv/Duo4HiYs1G2y1YhtBDDiGxLe2moIvfg9ajDzMZV29aXFw==", "dependencies": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-textarea": {"version": "9.3.60", "resolved": "https://registry.npmjs.org/@fluentui/react-textarea/-/react-textarea-9.3.60.tgz", "integrity": "sha512-wH4MBWT4EOgNH9FXTjcgH34oANUaoduhmVjffnxaPl3R767Ak0fZPG7kky7yrLMjTDUSwILsEj/q+hsN6o+7Ag==", "dependencies": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-theme": {"version": "9.1.16", "resolved": "https://registry.npmjs.org/@fluentui/react-theme/-/react-theme-9.1.16.tgz", "integrity": "sha512-QK2dGE5aQXN1UGdiEmGKpYGP3tHXIchLvFf8DEEOWnF4XBc9SiEPNFYkvLMJjHxZmDz4D670rsOPe0r5jFDEKQ==", "dependencies": {"@fluentui/tokens": "1.0.0-alpha.13", "@swc/helpers": "^0.5.1"}}, "node_modules/@fluentui/react-toast": {"version": "9.3.27", "resolved": "https://registry.npmjs.org/@fluentui/react-toast/-/react-toast-9.3.27.tgz", "integrity": "sha512-DbRAYyL5Bd/pcFiGHPpK+rQMyc4LBll9YBy496l97dGDO2HmqFuiwP74V1KznxLcr4inCNWwThIJws5VLFsJLg==", "dependencies": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1", "react-transition-group": "^4.4.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-toolbar": {"version": "9.1.66", "resolved": "https://registry.npmjs.org/@fluentui/react-toolbar/-/react-toolbar-9.1.66.tgz", "integrity": "sha512-ooNTp1R5MBZwiVK8fiJu29gE48vUx4NbXdwB2yHcCprasG3asjuoKQfOYM4+1NfFA0DetVrbK8L46IBeZyeBvA==", "dependencies": {"@fluentui/react-button": "^9.3.65", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-divider": "^9.2.58", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-radio": "^9.2.3", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-tooltip": {"version": "9.4.11", "resolved": "https://registry.npmjs.org/@fluentui/react-tooltip/-/react-tooltip-9.4.11.tgz", "integrity": "sha512-HXm8yYuAHJuczeFExco0WQSjO3DzDj5AJxqICHF8qtbtihUKfWpPnKM1qQWR+yJR2zc2jzvOEIzZXEkxSG+fSg==", "dependencies": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-positioning": "^9.12.4", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-tree": {"version": "9.4.25", "resolved": "https://registry.npmjs.org/@fluentui/react-tree/-/react-tree-9.4.25.tgz", "integrity": "sha512-7IMqnOiNFMRuPujnbxJUYD8AEh0z1OGXkdNkAeLyj3pkwuvQs9+TbaNtv5Z372YN+kwYF4EYalYcPuNsRlx7cQ==", "dependencies": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-avatar": "^9.6.9", "@fluentui/react-button": "^9.3.65", "@fluentui/react-checkbox": "^9.2.8", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-radio": "^9.2.3", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-utilities": {"version": "9.16.1", "resolved": "https://registry.npmjs.org/@fluentui/react-utilities/-/react-utilities-9.16.1.tgz", "integrity": "sha512-2wdwmgTFcVy14ZLbRNJ8Q6dCCBLekkJ8Znnok68gKRLDcwpPT3UjSraoU+DGjOA5BMfPppZBU8Yb5GqdIfd48g==", "dependencies": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-shared-contexts": "^9.14.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "react": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-virtualizer": {"version": "9.0.0-alpha.66", "resolved": "https://registry.npmjs.org/@fluentui/react-virtualizer/-/react-virtualizer-9.0.0-alpha.66.tgz", "integrity": "sha512-x/ZOAIAwctt7pvOBIzS4iZGU0ahiPhQFS7iAHksFkF9LimneaV92A/02dW0Cy4v7dv9wZNoosQwhS05Yx3DVDQ==", "dependencies": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-window-provider": {"version": "2.2.18", "resolved": "https://registry.npmjs.org/@fluentui/react-window-provider/-/react-window-provider-2.2.18.tgz", "integrity": "sha512-nBKqxd0P8NmIR0qzFvka1urE2LVbUm6cse1I1T7TcOVNYa5jDf5BrO06+JRZfwbn00IJqOnIVoP0qONqceypWQ==", "dependencies": {"@fluentui/set-version": "^8.2.14", "tslib": "^2.1.0"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "react": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/set-version": {"version": "8.2.14", "resolved": "https://registry.npmjs.org/@fluentui/set-version/-/set-version-8.2.14.tgz", "integrity": "sha512-f/QWJnSeyfAjGAqq57yjMb6a5ejPlwfzdExPmzFBuEOuupi8hHbV8Yno12XJcTW4I0KXEQGw+PUaM1aOf/j7jw==", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/@fluentui/style-utilities": {"version": "8.10.2", "resolved": "https://registry.npmjs.org/@fluentui/style-utilities/-/style-utilities-8.10.2.tgz", "integrity": "sha512-ocELtMb/85nBa3rSfiAIwYx6TydN+3rQqv1P0H/L7etYNNtxOfS86JSWfn8zAsHMejbwUKJ1ZsIKs47c598XGQ==", "dependencies": {"@fluentui/merge-styles": "^8.5.15", "@fluentui/set-version": "^8.2.14", "@fluentui/theme": "^2.6.41", "@fluentui/utilities": "^8.13.24", "@microsoft/load-themed-styles": "^1.10.26", "tslib": "^2.1.0"}}, "node_modules/@fluentui/theme": {"version": "2.6.41", "resolved": "https://registry.npmjs.org/@fluentui/theme/-/theme-2.6.41.tgz", "integrity": "sha512-h9RguEzqzJ0+59ys5Kkp7JtsjhDUxBLmQunu5rpHp5Mp788OtEjI/n1a9FIcOAL/priPSQwXN7RbuDpeP7+aSw==", "dependencies": {"@fluentui/merge-styles": "^8.5.15", "@fluentui/set-version": "^8.2.14", "@fluentui/utilities": "^8.13.24", "tslib": "^2.1.0"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "react": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/tokens": {"version": "1.0.0-alpha.13", "resolved": "https://registry.npmjs.org/@fluentui/tokens/-/tokens-1.0.0-alpha.13.tgz", "integrity": "sha512-IzYysTTBkAH7tQZxYKpzhxYnTJkvwXhjhTOpmERgnqTFifHTP8/vaQjJAAm7dI/9zlDx1oN+y/I+KzL9bDLHZQ==", "dependencies": {"@swc/helpers": "^0.5.1"}}, "node_modules/@fluentui/utilities": {"version": "8.13.24", "resolved": "https://registry.npmjs.org/@fluentui/utilities/-/utilities-8.13.24.tgz", "integrity": "sha512-/jo6hWCzTGCx06l2baAMwsjjBZ/dyMouls53uNaQLUGUUhUwXh/DcDDXMqLRJB3MaH9zvgfvRw61iKmm2s9fIA==", "dependencies": {"@fluentui/dom-utilities": "^2.2.14", "@fluentui/merge-styles": "^8.5.15", "@fluentui/set-version": "^8.2.14", "tslib": "^2.1.0"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "react": ">=16.8.0 <19.0.0"}}, "node_modules/@griffel/core": {"version": "1.15.2", "resolved": "https://registry.npmjs.org/@griffel/core/-/core-1.15.2.tgz", "integrity": "sha512-RlsIXoSS3gaYykUgxFpwKAs/DV9cRUKp3CW1kt3iPAtsDTWn/o+8bT1jvBws/tMM2GBu/Uc0EkaIzUPqD7uA+Q==", "dependencies": {"@emotion/hash": "^0.9.0", "@griffel/style-types": "^1.0.3", "csstype": "^3.1.3", "rtl-css-js": "^1.16.1", "stylis": "^4.2.0", "tslib": "^2.1.0"}}, "node_modules/@griffel/react": {"version": "1.5.20", "resolved": "https://registry.npmjs.org/@griffel/react/-/react-1.5.20.tgz", "integrity": "sha512-1P2yaPctENFSCwyPIYXBmgpNH68c0lc/jwSzPij1QATHDK1AASKuSeq6hW108I67RKjhRyHCcALshdZ3GcQXSg==", "dependencies": {"@griffel/core": "^1.15.2", "tslib": "^2.1.0"}, "peerDependencies": {"react": ">=16.8.0 <19.0.0"}}, "node_modules/@griffel/style-types": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/@griffel/style-types/-/style-types-1.0.3.tgz", "integrity": "sha512-AzbbYV/EobNIBtfMtyu2edFin895gjVxtu1nsRhTETUAIb0/LCZoue3Jd/kFLuPwe95rv5WRUBiQpVwJsrrFcw==", "dependencies": {"csstype": "^3.1.3"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.8", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz", "integrity": "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz", "integrity": "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "integrity": "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==", "dev": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "integrity": "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@microsoft/load-themed-styles": {"version": "1.10.295", "resolved": "https://registry.npmjs.org/@microsoft/load-themed-styles/-/load-themed-styles-1.10.295.tgz", "integrity": "sha512-W+IzEBw8a6LOOfRJM02dTT7BDZijxm+Z7lhtOAz1+y9vQm1Kdz9jlAO+qCEKsfxtUOmKilW8DIRqFw2aUgKeGg=="}, "node_modules/@react-spring/animated": {"version": "9.7.3", "resolved": "https://registry.npmjs.org/@react-spring/animated/-/animated-9.7.3.tgz", "integrity": "sha512-5CWeNJt9pNgyvuSzQH+uy2pvTg8Y4/OisoscZIR8/ZNLIOI+CatFBhGZpDGTF/OzdNFsAoGk3wiUYTwoJ0YIvw==", "dependencies": {"@react-spring/shared": "~9.7.3", "@react-spring/types": "~9.7.3"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0"}}, "node_modules/@react-spring/core": {"version": "9.7.3", "resolved": "https://registry.npmjs.org/@react-spring/core/-/core-9.7.3.tgz", "integrity": "sha512-IqFdPVf3ZOC1Cx7+M0cXf4odNLxDC+n7IN3MDcVCTIOSBfqEcBebSv+vlY5AhM0zw05PDbjKrNmBpzv/AqpjnQ==", "dependencies": {"@react-spring/animated": "~9.7.3", "@react-spring/shared": "~9.7.3", "@react-spring/types": "~9.7.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/react-spring/donate"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0"}}, "node_modules/@react-spring/shared": {"version": "9.7.3", "resolved": "https://registry.npmjs.org/@react-spring/shared/-/shared-9.7.3.tgz", "integrity": "sha512-NEopD+9S5xYyQ0pGtioacLhL2luflh6HACSSDUZOwLHoxA5eku1UPuqcJqjwSD6luKjjLfiLOspxo43FUHKKSA==", "dependencies": {"@react-spring/types": "~9.7.3"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0"}}, "node_modules/@react-spring/types": {"version": "9.7.3", "resolved": "https://registry.npmjs.org/@react-spring/types/-/types-9.7.3.tgz", "integrity": "sha512-Kpx/fQ/ZFX31OtlqVEFfgaD1ACzul4NksrvIgYfIFq9JpDHFwQkMVZ10tbo0FU/grje4rcL4EIrjekl3kYwgWw=="}, "node_modules/@react-spring/web": {"version": "9.7.3", "resolved": "https://registry.npmjs.org/@react-spring/web/-/web-9.7.3.tgz", "integrity": "sha512-BXt6BpS9aJL/QdVqEIX9YoUy8CE6TJrU0mNCqSoxdXlIeNcEBWOfIyE6B14ENNsyQKS3wOWkiJfco0tCr/9tUg==", "dependencies": {"@react-spring/animated": "~9.7.3", "@react-spring/core": "~9.7.3", "@react-spring/shared": "~9.7.3", "@react-spring/types": "~9.7.3"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0"}}, "node_modules/@remix-run/router": {"version": "1.14.2", "resolved": "https://registry.npmjs.org/@remix-run/router/-/router-1.14.2.tgz", "integrity": "sha512-ACXpdMM9hmKZww21yEqWwiLws/UPLhNKvimN8RrYSqPSvB3ov7sLvAcfvaxePeLvccTQKGdkDIhLYApZVDFuKg==", "engines": {"node": ">=14.0.0"}}, "node_modules/@rollup/plugin-typescript": {"version": "11.1.3", "resolved": "https://registry.npmjs.org/@rollup/plugin-typescript/-/plugin-typescript-11.1.3.tgz", "integrity": "sha512-8o6cNgN44kQBcpsUJTbTXMTtb87oR1O0zgP3Dxm71hrNgparap3VujgofEilTYJo+ivf2ke6uy3/E5QEaiRlDA==", "dependencies": {"@rollup/pluginutils": "^5.0.1", "resolve": "^1.22.1"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"rollup": "^2.14.0||^3.0.0", "tslib": "*", "typescript": ">=3.7.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}, "tslib": {"optional": true}}}, "node_modules/@rollup/pluginutils": {"version": "5.0.4", "resolved": "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-5.0.4.tgz", "integrity": "sha512-0KJnIoRI8A+a1dqOYLxH8vBf8bphDmty5QvIm2hqm7oFCFYKCAZWWd2hXgMibaPsNDhI0AtpYfQZJG47pt/k4g==", "dependencies": {"@types/estree": "^1.0.0", "estree-walker": "^2.0.2", "picomatch": "^2.3.1"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"rollup": "^1.20.0||^2.0.0||^3.0.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}}}, "node_modules/@rollup/rollup-android-arm-eabi": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.34.9.tgz", "integrity": "sha512-qZdlImWXur0CFakn2BJ2znJOdqYZKiedEPEVNTBrpfPjc/YuTGcaYZcdmNFTkUj3DU0ZM/AElcM8Ybww3xVLzA==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-android-arm64": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.9.tgz", "integrity": "sha512-4KW7P53h6HtJf5Y608T1ISKvNIYLWRKMvfnG0c44M6In4DQVU58HZFEVhWINDZKp7FZps98G3gxwC1sb0wXUUg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-darwin-arm64": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.34.9.tgz", "integrity": "sha512-0CY3/K54slrzLDjOA7TOjN1NuLKERBgk9nY5V34mhmuu673YNb+7ghaDUs6N0ujXR7fz5XaS5Aa6d2TNxZd0OQ==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-darwin-x64": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.34.9.tgz", "integrity": "sha512-eOojSEAi/acnsJVYRxnMkPFqcxSMFfrw7r2iD9Q32SGkb/Q9FpUY1UlAu1DH9T7j++gZ0lHjnm4OyH2vCI7l7Q==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-freebsd-arm64": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.34.9.tgz", "integrity": "sha512-2lzjQPJbN5UnHm7bHIUKFMulGTQwdvOkouJDpPysJS+QFBGDJqcfh+CxxtG23Ik/9tEvnebQiylYoazFMAgrYw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-freebsd-x64": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.34.9.tgz", "integrity": "sha512-SLl0hi2Ah2H7xQYd6Qaiu01kFPzQ+hqvdYSoOtHYg/zCIFs6t8sV95kaoqjzjFwuYQLtOI0RZre/Ke0nPaQV+g==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-linux-arm-gnueabihf": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.34.9.tgz", "integrity": "sha512-88I+D3TeKItrw+Y/2ud4Tw0+3CxQ2kLgu3QvrogZ0OfkmX/DEppehus7L3TS2Q4lpB+hYyxhkQiYPJ6Mf5/dPg==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm-musleabihf": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.34.9.tgz", "integrity": "sha512-3qyfWljSFHi9zH0KgtEPG4cBXHDFhwD8kwg6xLfHQ0IWuH9crp005GfoUUh/6w9/FWGBwEHg3lxK1iHRN1MFlA==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-gnu": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.34.9.tgz", "integrity": "sha512-6TZjPHjKZUQKmVKMUowF3ewHxctrRR09eYyvT5eFv8w/fXarEra83A2mHTVJLA5xU91aCNOUnM+DWFMSbQ0Nxw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-musl": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.34.9.tgz", "integrity": "sha512-LD2fytxZJZ6xzOKnMbIpgzFOuIKlxVOpiMAXawsAZ2mHBPEYOnLRK5TTEsID6z4eM23DuO88X0Tq1mErHMVq0A==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-loongarch64-gnu": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.34.9.tgz", "integrity": "sha512-dRAgTfDsn0TE0HI6cmo13hemKpVHOEyeciGtvlBTkpx/F65kTvShtY/EVyZEIfxFkV5JJTuQ9tP5HGBS0hfxIg==", "cpu": ["loong64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-powerpc64le-gnu": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.34.9.tgz", "integrity": "sha512-PHcNOAEhkoMSQtMf+rJofwisZqaU8iQ8EaSps58f5HYll9EAY5BSErCZ8qBDMVbq88h4UxaNPlbrKqfWP8RfJA==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-gnu": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.34.9.tgz", "integrity": "sha512-Z2i0Uy5G96KBYKjeQFKbbsB54xFOL5/y1P5wNBsbXB8yE+At3oh0DVMjQVzCJRJSfReiB2tX8T6HUFZ2k8iaKg==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-s390x-gnu": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.34.9.tgz", "integrity": "sha512-U+5SwTMoeYXoDzJX5dhDTxRltSrIax8KWwfaaYcynuJw8mT33W7oOgz0a+AaXtGuvhzTr2tVKh5UO8GVANTxyQ==", "cpu": ["s390x"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-gnu": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.34.9.tgz", "integrity": "sha512-FwBHNSOjUTQLP4MG7y6rR6qbGw4MFeQnIBrMe161QGaQoBQLqSUEKlHIiVgF3g/mb3lxlxzJOpIBhaP+C+KP2A==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-musl": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.34.9.tgz", "integrity": "sha512-cYRpV4650z2I3/s6+5/LONkjIz8MBeqrk+vPXV10ORBnshpn8S32bPqQ2Utv39jCiDcO2eJTuSlPXpnvmaIgRA==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-win32-arm64-msvc": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.9.tgz", "integrity": "sha512-z4mQK9dAN6byRA/vsSgQiPeuO63wdiDxZ9yg9iyX2QTzKuQM7T4xlBoeUP/J8uiFkqxkcWndWi+W7bXdPbt27Q==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-ia32-msvc": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.34.9.tgz", "integrity": "sha512-KB48mPtaoHy1AwDNkAJfHXvHp24H0ryZog28spEs0V48l3H1fr4i37tiyHsgKZJnCmvxsbATdZGBpbmxTE3a9w==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-x64-msvc": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.34.9.tgz", "integrity": "sha512-AyleYRPU7+rgkMWbEh71fQlrzRfeP6SyMnRf9XX4fCdDPAJumdSBqYEcWPMzVQ4ScAl7E4oFfK0GUVn77xSwbw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@swc/helpers": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.3.tgz", "integrity": "sha512-FaruWX6KdudYloq1AHD/4nU+UsMTdNE8CKyrseXWEcgjDAbvkwJg2QGPAnfIJLIWsjZOSPLOAykK6fuYp4vp4A==", "dependencies": {"tslib": "^2.4.0"}}, "node_modules/@types/babel__core": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz", "integrity": "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==", "dev": true, "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/@types/babel__generator": {"version": "7.6.8", "resolved": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.6.8.tgz", "integrity": "sha512-ASsj+tpEDsEiFr1arWrlN6V3mdfjRMZt6LtK/Vp/kreFLnr5QH5+DhvD5nINYZXzwJvXeGq+05iUXcAzVrqWtw==", "dev": true, "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/babel__template": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz", "integrity": "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==", "dev": true, "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@types/babel__traverse": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.5.tgz", "integrity": "sha512-WXCyOcRtH37HAUkpXhUduaxdm82b4GSlyTqajXviN4EfiuPgNYR109xMCKvpl6zPIpua0DGlMEDCq+g8EdoheQ==", "dev": true, "dependencies": {"@babel/types": "^7.20.7"}}, "node_modules/@types/dompurify": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/@types/dompurify/-/dompurify-3.0.3.tgz", "integrity": "sha512-odiGr/9/qMqjcBOe5UhcNLOFHSYmKFOyr+bJ/Xu3Qp4k1pNPAlNLUVNNLcLfjQI7+W7ObX58EdD3H+3p3voOvA==", "dev": true, "dependencies": {"@types/trusted-types": "*"}}, "node_modules/@types/estree": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/@types/estree/-/estree-1.0.6.tgz", "integrity": "sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==", "license": "MIT"}, "node_modules/@types/prop-types": {"version": "15.7.5", "resolved": "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.5.tgz", "integrity": "sha512-JCB8C6SnDoQf0cNycqd/35A7MjcnK+ZTqE7judS6o7utxUCg6imJg3QK2qzHKszlTjcj2cn+NwMB2i96ubpj7w=="}, "node_modules/@types/react": {"version": "18.2.48", "resolved": "https://registry.npmjs.org/@types/react/-/react-18.2.48.tgz", "integrity": "sha512-qboRCl6Ie70DQQG9hhNREz81jqC1cs9EVNcjQ1AU+jH6NFfSAhVVbrrY/+nSF+Bsk4AOwm9Qa61InvMCyV+H3w==", "dependencies": {"@types/prop-types": "*", "@types/scheduler": "*", "csstype": "^3.0.2"}}, "node_modules/@types/react-dom": {"version": "18.2.18", "resolved": "https://registry.npmjs.org/@types/react-dom/-/react-dom-18.2.18.tgz", "integrity": "sha512-TJxDm6OfAX2KJWJdMEVTwWke5Sc/E/RlnPGvGfS0W7+6ocy2xhDVQVh/KvC2Uf7kACs+gDytdusDSdWfWkaNzw==", "dependencies": {"@types/react": "*"}}, "node_modules/@types/scheduler": {"version": "0.16.2", "resolved": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.2.tgz", "integrity": "sha512-hppQEBDmlwhFAXKJX2KnWLYu5yMfi91yazPb2l+lbJiwW+wdo1gNeRA+3RgNSO39WYX2euey41KEwnqesU2Jew=="}, "node_modules/@types/trusted-types": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/@types/trusted-types/-/trusted-types-2.0.7.tgz", "integrity": "sha512-ScaPdn1dQczgbl0QFTeTOmVHFULt394XJgOQNoyVhZ6r2vLnMLJfBPd53SB52T/3G36VI1/g2MZaX0cwDuXsfw==", "devOptional": true, "license": "MIT"}, "node_modules/@vitejs/plugin-react": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.3.4.tgz", "integrity": "sha512-SCCPBJtYLdE8PX/7ZQAs1QAZ8Jqwih+0VBLum1EGqmCCQal+MIUqLCzj3ZUy8ufbC0cAM4LRlSTm7IQJwWT4ug==", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.26.0", "@babel/plugin-transform-react-jsx-self": "^7.25.9", "@babel/plugin-transform-react-jsx-source": "^7.25.9", "@types/babel__core": "^7.20.5", "react-refresh": "^0.14.2"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0 || ^6.0.0"}}, "node_modules/browserslist": {"version": "4.24.4", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.24.4.tgz", "integrity": "sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001688", "electron-to-chromium": "^1.5.73", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.1"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/caniuse-lite": {"version": "1.0.30001702", "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001702.tgz", "integrity": "sha512-LoPe/D7zioC0REI5W73PeR1e1MLCipRGq/VkovJnd6Df+QVqT+vT33OXCp8QUd7kA7RZrHWxb1B36OQKI/0gOA==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/convert-source-map": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz", "integrity": "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==", "dev": true, "license": "MIT"}, "node_modules/csstype": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz", "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw=="}, "node_modules/debug": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz", "integrity": "sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/dom-helpers": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz", "integrity": "sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==", "dependencies": {"@babel/runtime": "^7.8.7", "csstype": "^3.0.2"}}, "node_modules/dompurify": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/dompurify/-/dompurify-3.2.4.tgz", "integrity": "sha512-ysFSFEDVduQpyhzAob/kkuJjf5zWkZD8/A9ywSp1byueyuCfHamrCBa14/Oc2iiB0e51B+NpxSl5gmzn+Ms/mg==", "license": "(MPL-2.0 OR Apache-2.0)", "optionalDependencies": {"@types/trusted-types": "^2.0.7"}}, "node_modules/electron-to-chromium": {"version": "1.5.112", "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.112.tgz", "integrity": "sha512-oen93kVyqSb3l+ziUgzIOlWt/oOuy4zRmpwestMn4rhFWAoFJeFuCVte9F2fASjeZZo7l/Cif9TiyrdW4CwEMA==", "dev": true, "license": "ISC"}, "node_modules/esbuild": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/esbuild/-/esbuild-0.25.0.tgz", "integrity": "sha512-BXq5mqc8ltbaN34cDqWuYKyNhX8D/Z0J1xdtdQ8UcIIIyJyz+ZMKUt58tF3SrZ85jcfN/PZYhjR5uDQAYNVbuw==", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.25.0", "@esbuild/android-arm": "0.25.0", "@esbuild/android-arm64": "0.25.0", "@esbuild/android-x64": "0.25.0", "@esbuild/darwin-arm64": "0.25.0", "@esbuild/darwin-x64": "0.25.0", "@esbuild/freebsd-arm64": "0.25.0", "@esbuild/freebsd-x64": "0.25.0", "@esbuild/linux-arm": "0.25.0", "@esbuild/linux-arm64": "0.25.0", "@esbuild/linux-ia32": "0.25.0", "@esbuild/linux-loong64": "0.25.0", "@esbuild/linux-mips64el": "0.25.0", "@esbuild/linux-ppc64": "0.25.0", "@esbuild/linux-riscv64": "0.25.0", "@esbuild/linux-s390x": "0.25.0", "@esbuild/linux-x64": "0.25.0", "@esbuild/netbsd-arm64": "0.25.0", "@esbuild/netbsd-x64": "0.25.0", "@esbuild/openbsd-arm64": "0.25.0", "@esbuild/openbsd-x64": "0.25.0", "@esbuild/sunos-x64": "0.25.0", "@esbuild/win32-arm64": "0.25.0", "@esbuild/win32-ia32": "0.25.0", "@esbuild/win32-x64": "0.25.0"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/estree-walker": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz", "integrity": "sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w=="}, "node_modules/frontend": {"resolved": "", "link": true}, "node_modules/fsevents": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="}, "node_modules/gensync": {"version": "1.0.0-beta.2", "resolved": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/globals": {"version": "11.12.0", "resolved": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz", "integrity": "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/has": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/has/-/has-1.0.3.tgz", "integrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==", "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/is-core-module": {"version": "2.13.0", "resolved": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.13.0.tgz", "integrity": "sha512-Z7dk6Qo8pOCp3l4tsX2C5ZVas4V+UxwQodwZhLopL91TX8UyyHEXafPcyoeeWuLrwzHcr3igO78wNLwHJHsMCQ==", "dependencies": {"has": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="}, "node_modules/jsesc": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz", "integrity": "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==", "dev": true, "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json5": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/keyborg": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/keyborg/-/keyborg-2.4.1.tgz", "integrity": "sha512-B9EZwDd36WKlIq6JmimaTsTDx5E0aUqZcxtgTfK66ut1FbRXYhBmiB7Al2qKzB7CCX9C49sTBiiyVzsXCA6J4Q=="}, "node_modules/loose-envify": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/lru-cache": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==", "dev": true, "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "dev": true, "license": "MIT"}, "node_modules/nanoid": {"version": "3.3.8", "resolved": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.8.tgz", "integrity": "sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/ndjson-readablestream": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/ndjson-readablestream/-/ndjson-readablestream-1.0.7.tgz", "integrity": "sha512-4DDTwYTV4yRnCoXparQTF3JeahTNkLVy7XlA0RHXzAqQ3uU8vcu97bNW8rXAQOKQVJGs2aZoX+7cbvfs0LENEA=="}, "node_modules/node-releases": {"version": "2.0.19", "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz", "integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==", "dev": true, "license": "MIT"}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "engines": {"node": ">=0.10.0"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "dev": true, "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/postcss": {"version": "8.5.3", "resolved": "https://registry.npmjs.org/postcss/-/postcss-8.5.3.tgz", "integrity": "sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.8", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/prettier": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/prettier/-/prettier-3.0.3.tgz", "integrity": "sha512-L/4pUDMxcNa8R/EthV08Zt42WBO4h1rarVtK0K+QJG0X187OLo7l699jWw0GKuwzkPQ//jMFA/8Xm6Fh3J/DAg==", "dev": true, "bin": {"prettier": "bin/prettier.cjs"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/prop-types": {"version": "15.8.1", "resolved": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz", "integrity": "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "node_modules/prop-types/node_modules/react-is": {"version": "16.13.1", "resolved": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz", "integrity": "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="}, "node_modules/react": {"version": "18.2.0", "resolved": "https://registry.npmjs.org/react/-/react-18.2.0.tgz", "integrity": "sha512-/3IjMdb2L9QbBdWiW5e3P2/npwMBaU9mHCSCUzNln0ZCYbcfTsGbTJrU/kGemdH2IWmB2ioZ+zkxtmq6g09fGQ==", "dependencies": {"loose-envify": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/react-dom": {"version": "18.2.0", "resolved": "https://registry.npmjs.org/react-dom/-/react-dom-18.2.0.tgz", "integrity": "sha512-6IMTriUmvsjHUjNtEDudZfuDQUoWXVxKHhlEGSk81n4YFS+r/Kl99wXiwlVXtPBtJenozv2P+hxDsw9eA7Xo6g==", "dependencies": {"loose-envify": "^1.1.0", "scheduler": "^0.23.0"}, "peerDependencies": {"react": "^18.2.0"}}, "node_modules/react-dom/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/react-is": {"version": "17.0.2", "resolved": "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz", "integrity": "sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w=="}, "node_modules/react-refresh": {"version": "0.14.2", "resolved": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.14.2.tgz", "integrity": "sha512-jCvmsr+1IUSMUyzOkRcvnVbX3ZYC6g9TDrDbFuFmRDq7PD4yaGbLKNQL6k2jnArV8hjYxh7hVhAZB6s9HDGpZA==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/react-router": {"version": "6.21.3", "resolved": "https://registry.npmjs.org/react-router/-/react-router-6.21.3.tgz", "integrity": "sha512-a0H638ZXULv1OdkmiK6s6itNhoy33ywxmUFT/xtSoVyf9VnC7n7+VT4LjVzdIHSaF5TIh9ylUgxMXksHTgGrKg==", "dependencies": {"@remix-run/router": "1.14.2"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"react": ">=16.8"}}, "node_modules/react-router-dom": {"version": "6.21.3", "resolved": "https://registry.npmjs.org/react-router-dom/-/react-router-dom-6.21.3.tgz", "integrity": "sha512-kNzubk7n4YHSrErzjLK72j0B5i969GsuCGazRl3G6j1zqZBLjuSlYBdVdkDOgzGdPIffUOc9nmgiadTEVoq91g==", "dependencies": {"@remix-run/router": "1.14.2", "react-router": "6.21.3"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": ">=16.8"}}, "node_modules/react-transition-group": {"version": "4.4.5", "resolved": "https://registry.npmjs.org/react-transition-group/-/react-transition-group-4.4.5.tgz", "integrity": "sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==", "dependencies": {"@babel/runtime": "^7.5.5", "dom-helpers": "^5.0.1", "loose-envify": "^1.4.0", "prop-types": "^15.6.2"}, "peerDependencies": {"react": ">=16.6.0", "react-dom": ">=16.6.0"}}, "node_modules/regenerator-runtime": {"version": "0.14.1", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz", "integrity": "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw=="}, "node_modules/resolve": {"version": "1.22.4", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.22.4.tgz", "integrity": "sha512-PXNdCiPqDqeUou+w1C2eTQbNfxKSuMxqTCuvlmmMsk1NWHL5fRrhY6Pl0qEYYc6+QqGClco1Qj8XnjPego4wfg==", "dependencies": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/rollup": {"version": "3.29.5", "resolved": "https://registry.npmjs.org/rollup/-/rollup-3.29.5.tgz", "integrity": "sha512-GVsDdsbJzzy4S/v3dqWPJ7EfvZJfCHiDqe80IyrF59LYuP+e6U1LJoUqeuqRbwAWoMNoXivMNeNAOf5E22VA1w==", "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/rtl-css-js": {"version": "1.16.1", "resolved": "https://registry.npmjs.org/rtl-css-js/-/rtl-css-js-1.16.1.tgz", "integrity": "sha512-lRQgou1mu19e+Ya0LsTvKrVJ5TYUbqCVPAiImX3UfLTenarvPUl1QFdvu5Z3PYmHT9RCcwIfbjRQBntExyj3Zg==", "dependencies": {"@babel/runtime": "^7.1.2"}}, "node_modules/scheduler": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.20.2.tgz", "integrity": "sha512-2eWfGgAqqWFGqtdMmcL5zCMK1U8KlXv8SQFGglL3CEtd0aDVDWgeF/YoCmvln55m5zSk3J/20hTaSBeSObsQDQ==", "dependencies": {"loose-envify": "^1.1.0", "object-assign": "^4.1.1"}}, "node_modules/semver": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/stylis": {"version": "4.3.1", "resolved": "https://registry.npmjs.org/stylis/-/stylis-4.3.1.tgz", "integrity": "sha512-EQepAV+wMsIaGVGX1RECzgrcqRRU/0sYOHkeLsZ3fzHaHXZy4DaOOX0vOlGQdlsjkh3mFHAIlVimpwAs4dslyQ=="}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tabster": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/tabster/-/tabster-5.2.0.tgz", "integrity": "sha512-cSi3a0gGeM9Co/gTKHlhTFfiitwVjcA+kP9lJux0U7QaRrZox1yYrfbwZhJXM7N0fux7BgvCYaOxME5k0EQ0tA==", "dependencies": {"keyborg": "^2.2.0", "tslib": "^2.3.1"}}, "node_modules/tinyglobby": {"version": "0.2.12", "resolved": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.12.tgz", "integrity": "sha512-qkf4trmKSIiMTs/E63cxH+ojC2unam7rJ0WrauAzpT3ECNTxGRMlaXxVbfxMUC/w0LaYk6jQ4y/nGR9uBO3tww==", "dev": true, "license": "MIT", "dependencies": {"fdir": "^6.4.3", "picomatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}}, "node_modules/tinyglobby/node_modules/fdir": {"version": "6.4.3", "resolved": "https://registry.npmjs.org/fdir/-/fdir-6.4.3.tgz", "integrity": "sha512-PMXmW2y1hDDfTSRc9gaXIuCCRpuoz3Kaz8cUelp3smouvfT632ozg2vrT6lJsHKKOF59YLbOGfAWGUcKEfRMQw==", "dev": true, "license": "MIT", "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/tinyglobby/node_modules/picomatch": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-4.0.2.tgz", "integrity": "sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/tslib": {"version": "2.5.0", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.5.0.tgz", "integrity": "sha512-336iVw3rtn2BUK7ORdIAHTyxHGRIHVReokCR3XjbckJMK7ms8FysBfhLR8IXnAgy7T0PTPNBWKiH514FOW/WSg=="}, "node_modules/typescript": {"version": "5.2.2", "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.2.2.tgz", "integrity": "sha512-mI4WrpHsbCIcwT9cF4FZvr80QUeKvsUsUvKDoR+X/7XHQH98xYD8YHZg7ANtz2GtZt/CBq2QJ0thkGJMHfqc1w==", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/update-browserslist-db": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "integrity": "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/use-disposable": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/use-disposable/-/use-disposable-1.0.2.tgz", "integrity": "sha512-UMaXVlV77dWOu4GqAFNjRzHzowYKUKbJBQfCexvahrYeIz4OkUYUjna4Tjjdf92NH8Nm8J7wEfFRgTIwYjO5jg==", "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.8.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/vite": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/vite/-/vite-6.3.1.tgz", "integrity": "sha512-kkzzkqtMESYklo96HKKPE5KKLkC1amlsqt+RjFMlX2AvbRB/0wghap19NdBxxwGZ+h/C6DLCrcEphPIItlGrRQ==", "dev": true, "license": "MIT", "dependencies": {"esbuild": "^0.25.0", "fdir": "^6.4.3", "picomatch": "^4.0.2", "postcss": "^8.5.3", "rollup": "^4.34.9", "tinyglobby": "^0.2.12"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0", "jiti": ">=1.21.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "sass-embedded": "*", "stylus": "*", "sugarss": "*", "terser": "^5.16.0", "tsx": "^4.8.1", "yaml": "^2.4.2"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "jiti": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}, "tsx": {"optional": true}, "yaml": {"optional": true}}}, "node_modules/vite/node_modules/fdir": {"version": "6.4.3", "resolved": "https://registry.npmjs.org/fdir/-/fdir-6.4.3.tgz", "integrity": "sha512-PMXmW2y1hDDfTSRc9gaXIuCCRpuoz3Kaz8cUelp3smouvfT632ozg2vrT6lJsHKKOF59YLbOGfAWGUcKEfRMQw==", "dev": true, "license": "MIT", "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/vite/node_modules/picomatch": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-4.0.2.tgz", "integrity": "sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/vite/node_modules/rollup": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/rollup/-/rollup-4.34.9.tgz", "integrity": "sha512-nF5XYqWWp9hx/LrpC8sZvvvmq0TeTjQgaZHYmAgwysT9nh8sWnZhBnM8ZyVbbJFIQBLwHDNoMqsBZBbUo4U8sQ==", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "1.0.6"}, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.34.9", "@rollup/rollup-android-arm64": "4.34.9", "@rollup/rollup-darwin-arm64": "4.34.9", "@rollup/rollup-darwin-x64": "4.34.9", "@rollup/rollup-freebsd-arm64": "4.34.9", "@rollup/rollup-freebsd-x64": "4.34.9", "@rollup/rollup-linux-arm-gnueabihf": "4.34.9", "@rollup/rollup-linux-arm-musleabihf": "4.34.9", "@rollup/rollup-linux-arm64-gnu": "4.34.9", "@rollup/rollup-linux-arm64-musl": "4.34.9", "@rollup/rollup-linux-loongarch64-gnu": "4.34.9", "@rollup/rollup-linux-powerpc64le-gnu": "4.34.9", "@rollup/rollup-linux-riscv64-gnu": "4.34.9", "@rollup/rollup-linux-s390x-gnu": "4.34.9", "@rollup/rollup-linux-x64-gnu": "4.34.9", "@rollup/rollup-linux-x64-musl": "4.34.9", "@rollup/rollup-win32-arm64-msvc": "4.34.9", "@rollup/rollup-win32-ia32-msvc": "4.34.9", "@rollup/rollup-win32-x64-msvc": "4.34.9", "fsevents": "~2.3.2"}}, "node_modules/yallist": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==", "dev": true, "license": "ISC"}}, "dependencies": {"@ampproject/remapping": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz", "integrity": "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==", "dev": true, "requires": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}}, "@azure/msal-browser": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/@azure/msal-browser/-/msal-browser-3.2.0.tgz", "integrity": "sha512-le2qutddMiq0i3ErQaLKuwP1DpNgdd9iXPs3fSCsLuBrdGg9B4/j4ArCAHCwgxA82Ydj9BcqtMIL5BSWwU+P5A==", "requires": {"@azure/msal-common": "14.1.0"}}, "@azure/msal-common": {"version": "14.1.0", "resolved": "https://registry.npmjs.org/@azure/msal-common/-/msal-common-14.1.0.tgz", "integrity": "sha512-xphmhcfl5VL+uq5//VKMwQn+wfEZLMKNpFCcMi8Ur8ej5UT166g6chBsxgMzc9xo9Y24R9FB3m/tjDiV03xMIA=="}, "@azure/msal-react": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/@azure/msal-react/-/msal-react-2.0.4.tgz", "integrity": "sha512-BujRm5FBDWYXyr3pnmubS4dIqZMlurYVtV2AyztoeAFUd+nh3XQZD9knHBqTyu53IDjhCCvUPUke/jSkv5WGlg==", "requires": {"@rollup/plugin-typescript": "^11.1.0", "rollup": "^3.20.2"}}, "@babel/code-frame": {"version": "7.26.2", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.26.2.tgz", "integrity": "sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.25.9", "js-tokens": "^4.0.0", "picocolors": "^1.0.0"}}, "@babel/compat-data": {"version": "7.26.8", "resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.26.8.tgz", "integrity": "sha512-oH5UPLMWR3L2wEFLnFJ1TZXqHufiTKAiLfqw5zkhS4dKXLJ10yVztfil/twG8EDTA4F/tvVNw9nOl4ZMslB8rQ==", "dev": true}, "@babel/core": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.26.9.tgz", "integrity": "sha512-lWBYIrF7qK5+GjY5Uy+/hEgp8OJWOD/rpy74GplYRhEauvbHDeFB8t5hPOZxCZ0Oxf4Cc36tK51/l3ymJysrKw==", "dev": true, "requires": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.26.2", "@babel/generator": "^7.26.9", "@babel/helper-compilation-targets": "^7.26.5", "@babel/helper-module-transforms": "^7.26.0", "@babel/helpers": "^7.26.9", "@babel/parser": "^7.26.9", "@babel/template": "^7.26.9", "@babel/traverse": "^7.26.9", "@babel/types": "^7.26.9", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}}, "@babel/generator": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.26.9.tgz", "integrity": "sha512-kEWdzjOAUMW4hAyrzJ0ZaTOu9OmpyDIQicIh0zg0EEcEkYXZb2TjtBhnHi2ViX7PKwZqF4xwqfAm299/QMP3lg==", "dev": true, "requires": {"@babel/parser": "^7.26.9", "@babel/types": "^7.26.9", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-compilation-targets": {"version": "7.26.5", "resolved": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.26.5.tgz", "integrity": "sha512-IXuyn5EkouFJscIDuFF5EsiSolseme1s0CZB+QxVugqJLYmKdxI1VfIBOst0SUu4rnk2Z7kqTwmoO1lp3HIfnA==", "dev": true, "requires": {"@babel/compat-data": "^7.26.5", "@babel/helper-validator-option": "^7.25.9", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}}, "@babel/helper-module-imports": {"version": "7.25.9", "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.25.9.tgz", "integrity": "sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==", "dev": true, "requires": {"@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}}, "@babel/helper-module-transforms": {"version": "7.26.0", "resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.26.0.tgz", "integrity": "sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==", "dev": true, "requires": {"@babel/helper-module-imports": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/traverse": "^7.25.9"}}, "@babel/helper-plugin-utils": {"version": "7.26.5", "resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz", "integrity": "sha512-RS+jZcRdZdRFzMyr+wcsaqOmld1/EqTghfaBGQQd/WnRdzdlvSZ//kF7U8VQTxf1ynZ4cjUcYgjVGx13ewNPMg==", "dev": true}, "@babel/helper-string-parser": {"version": "7.25.9", "resolved": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.25.9.tgz", "integrity": "sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==", "dev": true}, "@babel/helper-validator-identifier": {"version": "7.25.9", "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.9.tgz", "integrity": "sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==", "dev": true}, "@babel/helper-validator-option": {"version": "7.25.9", "resolved": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.25.9.tgz", "integrity": "sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==", "dev": true}, "@babel/helpers": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.26.9.tgz", "integrity": "sha512-Mz/4+y8udxBKdmzt/UjPACs4G3j5SshJJEFFKxlCGPydG4JAHXxjWjAwjd09tf6oINvl1VfMJo+nB7H2YKQ0dA==", "dev": true, "requires": {"@babel/template": "^7.26.9", "@babel/types": "^7.26.9"}}, "@babel/parser": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.26.9.tgz", "integrity": "sha512-81NWa1njQblgZbQHxWHpxxCzNsa3ZwvFqpUg7P+NNUU6f3UU2jBEg4OlF/J6rl8+PQGh1q6/zWScd001YwcA5A==", "dev": true, "requires": {"@babel/types": "^7.26.9"}}, "@babel/plugin-transform-react-jsx-self": {"version": "7.25.9", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.25.9.tgz", "integrity": "sha512-y8quW6p0WHkEhmErnfe58r7x0A70uKphQm8Sp8cV7tjNQwK56sNVK0M73LK3WuYmsuyrftut4xAkjjgU0twaMg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.25.9"}}, "@babel/plugin-transform-react-jsx-source": {"version": "7.25.9", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.25.9.tgz", "integrity": "sha512-+iqjT8xmXhhYv4/uiYd8FNQsraMFZIfxVSqxxVSZP0WbbSAWvBXAul0m/zu+7Vv4O/3WtApy9pmaTMiumEZgfg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.25.9"}}, "@babel/runtime": {"version": "7.27.0", "resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.0.tgz", "integrity": "sha512-VtPOkrdPHZsKc/clNqyi9WUA8TINkZ4cGk63UUE3u4pmB2k+ZMQRDuIOagv8UVd6j7k0T3+RRIb7beKTebNbcw==", "requires": {"regenerator-runtime": "^0.14.0"}}, "@babel/template": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/template/-/template-7.26.9.tgz", "integrity": "sha512-qyRplbeIpNZhmzOysF/wFMuP9sctmh2cFzRAZOn1YapxBsE1i9bJIY586R/WBLfLcmcBlM8ROBiQURnnNy+zfA==", "dev": true, "requires": {"@babel/code-frame": "^7.26.2", "@babel/parser": "^7.26.9", "@babel/types": "^7.26.9"}}, "@babel/traverse": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.26.9.tgz", "integrity": "sha512-ZYW7L+pL8ahU5fXmNbPF+iZFHCv5scFak7MZ9bwaRPLUhHh7QQEMjZUg0HevihoqCM5iSYHN61EyCoZvqC+bxg==", "dev": true, "requires": {"@babel/code-frame": "^7.26.2", "@babel/generator": "^7.26.9", "@babel/parser": "^7.26.9", "@babel/template": "^7.26.9", "@babel/types": "^7.26.9", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.26.9.tgz", "integrity": "sha512-Y3IR1cRnOxOCDvMmNiym7XpXQ93iGDDPHx+Zj+NM+rg0fBaShfQLkg+hKPaZCEvg5N/LeCo4+Rj/i3FuJsIQaw==", "dev": true, "requires": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}}, "@emotion/hash": {"version": "0.9.1", "resolved": "https://registry.npmjs.org/@emotion/hash/-/hash-0.9.1.tgz", "integrity": "sha512-gJB6HLm5rYwSLI6PQa+X1t5CFGrv1J1TWG+sOyMCeKz2ojaj6Fnl/rZEspogG+cvqbt4AE/2eIyD2QfLKTBNlQ=="}, "@esbuild/aix-ppc64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/aix-ppc64/-/aix-ppc64-0.25.0.tgz", "integrity": "sha512-O7vun9Sf8DFjH2UtqK8Ku3LkquL9SZL8OLY1T5NZkA34+wG3OQF7cl4Ql8vdNzM6fzBbYfLaiRLIOZ+2FOCgBQ==", "dev": true, "optional": true}, "@esbuild/android-arm": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.25.0.tgz", "integrity": "sha512-PTyWCYYiU0+1eJKmw21lWtC+d08JDZPQ5g+kFyxP0V+es6VPPSUhM6zk8iImp2jbV6GwjX4pap0JFbUQN65X1g==", "dev": true, "optional": true}, "@esbuild/android-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/android-arm64/-/android-arm64-0.25.0.tgz", "integrity": "sha512-grvv8WncGjDSyUBjN9yHXNt+cq0snxXbDxy5pJtzMKGmmpPxeAmAhWxXI+01lU5rwZomDgD3kJwulEnhTRUd6g==", "dev": true, "optional": true}, "@esbuild/android-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/android-x64/-/android-x64-0.25.0.tgz", "integrity": "sha512-m/ix7SfKG5buCnxasr52+LI78SQ+wgdENi9CqyCXwjVR2X4Jkz+BpC3le3AoBPYTC9NHklwngVXvbJ9/Akhrfg==", "dev": true, "optional": true}, "@esbuild/darwin-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/darwin-arm64/-/darwin-arm64-0.25.0.tgz", "integrity": "sha512-mVwdUb5SRkPayVadIOI78K7aAnPamoeFR2bT5nszFUZ9P8UpK4ratOdYbZZXYSqPKMHfS1wdHCJk1P1EZpRdvw==", "dev": true, "optional": true}, "@esbuild/darwin-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/darwin-x64/-/darwin-x64-0.25.0.tgz", "integrity": "sha512-DgDaYsPWFTS4S3nWpFcMn/33ZZwAAeAFKNHNa1QN0rI4pUjgqf0f7ONmXf6d22tqTY+H9FNdgeaAa+YIFUn2Rg==", "dev": true, "optional": true}, "@esbuild/freebsd-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.0.tgz", "integrity": "sha512-VN4ocxy6dxefN1MepBx/iD1dH5K8qNtNe227I0mnTRjry8tj5MRk4zprLEdG8WPyAPb93/e4pSgi1SoHdgOa4w==", "dev": true, "optional": true}, "@esbuild/freebsd-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.25.0.tgz", "integrity": "sha512-mrSgt7lCh07FY+hDD1TxiTyIHyttn6vnjesnPoVDNmDfOmggTLXRv8Id5fNZey1gl/V2dyVK1VXXqVsQIiAk+A==", "dev": true, "optional": true}, "@esbuild/linux-arm": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.25.0.tgz", "integrity": "sha512-vkB3IYj2IDo3g9xX7HqhPYxVkNQe8qTK55fraQyTzTX/fxaDtXiEnavv9geOsonh2Fd2RMB+i5cbhu2zMNWJwg==", "dev": true, "optional": true}, "@esbuild/linux-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-arm64/-/linux-arm64-0.25.0.tgz", "integrity": "sha512-9QAQjTWNDM/Vk2bgBl17yWuZxZNQIF0OUUuPZRKoDtqF2k4EtYbpyiG5/Dk7nqeK6kIJWPYldkOcBqjXjrUlmg==", "dev": true, "optional": true}, "@esbuild/linux-ia32": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-ia32/-/linux-ia32-0.25.0.tgz", "integrity": "sha512-43ET5bHbphBegyeqLb7I1eYn2P/JYGNmzzdidq/w0T8E2SsYL1U6un2NFROFRg1JZLTzdCoRomg8Rvf9M6W6Gg==", "dev": true, "optional": true}, "@esbuild/linux-loong64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-loong64/-/linux-loong64-0.25.0.tgz", "integrity": "sha512-fC95c/xyNFueMhClxJmeRIj2yrSMdDfmqJnyOY4ZqsALkDrrKJfIg5NTMSzVBr5YW1jf+l7/cndBfP3MSDpoHw==", "dev": true, "optional": true}, "@esbuild/linux-mips64el": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.25.0.tgz", "integrity": "sha512-nkAMFju7KDW73T1DdH7glcyIptm95a7Le8irTQNO/qtkoyypZAnjchQgooFUDQhNAy4iu08N79W4T4pMBwhPwQ==", "dev": true, "optional": true}, "@esbuild/linux-ppc64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-ppc64/-/linux-ppc64-0.25.0.tgz", "integrity": "sha512-NhyOejdhRGS8Iwv+KKR2zTq2PpysF9XqY+Zk77vQHqNbo/PwZCzB5/h7VGuREZm1fixhs4Q/qWRSi5zmAiO4Fw==", "dev": true, "optional": true}, "@esbuild/linux-riscv64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-riscv64/-/linux-riscv64-0.25.0.tgz", "integrity": "sha512-5S/rbP5OY+GHLC5qXp1y/Mx//e92L1YDqkiBbO9TQOvuFXM+iDqUNG5XopAnXoRH3FjIUDkeGcY1cgNvnXp/kA==", "dev": true, "optional": true}, "@esbuild/linux-s390x": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.25.0.tgz", "integrity": "sha512-XM2BFsEBz0Fw37V0zU4CXfcfuACMrppsMFKdYY2WuTS3yi8O1nFOhil/xhKTmE1nPmVyvQJjJivgDT+xh8pXJA==", "dev": true, "optional": true}, "@esbuild/linux-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-x64/-/linux-x64-0.25.0.tgz", "integrity": "sha512-9yl91rHw/cpwMCNytUDxwj2XjFpxML0y9HAOH9pNVQDpQrBxHy01Dx+vaMu0N1CKa/RzBD2hB4u//nfc+Sd3Cw==", "dev": true, "optional": true}, "@esbuild/netbsd-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.0.tgz", "integrity": "sha512-RuG4PSMPFfrkH6UwCAqBzauBWTygTvb1nxWasEJooGSJ/NwRw7b2HOwyRTQIU97Hq37l3npXoZGYMy3b3xYvPw==", "dev": true, "optional": true}, "@esbuild/netbsd-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.25.0.tgz", "integrity": "sha512-jl+qisSB5jk01N5f7sPCsBENCOlPiS/xptD5yxOx2oqQfyourJwIKLRA2yqWdifj3owQZCL2sn6o08dBzZGQzA==", "dev": true, "optional": true}, "@esbuild/openbsd-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.0.tgz", "integrity": "sha512-21sUNbq2r84YE+SJDfaQRvdgznTD8Xc0oc3p3iW/a1EVWeNj/SdUCbm5U0itZPQYRuRTW20fPMWMpcrciH2EJw==", "dev": true, "optional": true}, "@esbuild/openbsd-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/openbsd-x64/-/openbsd-x64-0.25.0.tgz", "integrity": "sha512-2gwwriSMPcCFRlPlKx3zLQhfN/2WjJ2NSlg5TKLQOJdV0mSxIcYNTMhk3H3ulL/cak+Xj0lY1Ym9ysDV1igceg==", "dev": true, "optional": true}, "@esbuild/sunos-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.25.0.tgz", "integrity": "sha512-bxI7ThgLzPrPz484/S9jLlvUAHYMzy6I0XiU1ZMeAEOBcS0VePBFxh1JjTQt3Xiat5b6Oh4x7UC7IwKQKIJRIg==", "dev": true, "optional": true}, "@esbuild/win32-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.25.0.tgz", "integrity": "sha512-ZUAc2YK6JW89xTbXvftxdnYy3m4iHIkDtK3CLce8wg8M2L+YZhIvO1DKpxrd0Yr59AeNNkTiic9YLf6FTtXWMw==", "dev": true, "optional": true}, "@esbuild/win32-ia32": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/win32-ia32/-/win32-ia32-0.25.0.tgz", "integrity": "sha512-eSNxISBu8XweVEWG31/JzjkIGbGIJN/TrRoiSVZwZ6pkC6VX4Im/WV2cz559/TXLcYbcrDN8JtKgd9DJVIo8GA==", "dev": true, "optional": true}, "@esbuild/win32-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.25.0.tgz", "integrity": "sha512-ZENoHJBxA20C2zFzh6AI4fT6RraMzjYw4xKWemRTRmRVtN9c5DcH9r/f2ihEkMjOW5eGgrwCslG/+Y/3bL+DHQ==", "dev": true, "optional": true}, "@floating-ui/core": {"version": "1.5.3", "resolved": "https://registry.npmjs.org/@floating-ui/core/-/core-1.5.3.tgz", "integrity": "sha512-O0WKDOo0yhJuugCx6trZQj5jVJ9yR0ystG2JaNAemYUWce+pmM6WUEFIibnWyEJKdrDxhm75NoSRME35FNaM/Q==", "requires": {"@floating-ui/utils": "^0.2.0"}}, "@floating-ui/devtools": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@floating-ui/devtools/-/devtools-0.2.1.tgz", "integrity": "sha512-8PHJLbD6VhBh+LJ1uty/Bz30qs02NXCE5u8WpOhSewlYXUWl03GNXknr9AS2yaAWJEQaY27x7eByJs44gODBcw==", "requires": {}}, "@floating-ui/dom": {"version": "1.5.4", "resolved": "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.5.4.tgz", "integrity": "sha512-jByEsHIY+eEdCjnTVu+E3ephzTOzkQ8hgUfGwos+bg7NlH33Zc5uO+QHz1mrQUOgIKKDD1RtS201P9NvAfq3XQ==", "requires": {"@floating-ui/core": "^1.5.3", "@floating-ui/utils": "^0.2.0"}}, "@floating-ui/utils": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.1.tgz", "integrity": "sha512-9TANp6GPoMtYzQdt54kfAyMmz1+osLlXdg2ENroU7zzrtflTLrrC/lgrIfaSe+Wu0b89GKccT7vxXA0MoAIO+Q=="}, "@fluentui/date-time-utilities": {"version": "8.5.16", "resolved": "https://registry.npmjs.org/@fluentui/date-time-utilities/-/date-time-utilities-8.5.16.tgz", "integrity": "sha512-l+mLfJ2VhdHjBpELLLPDaWgT7GMLynm2aqR7SttbEb6Jh7hc/7ck1MWm93RTb3gYVHYai8SENqimNcvIxHt/zg==", "requires": {"@fluentui/set-version": "^8.2.14", "tslib": "^2.1.0"}}, "@fluentui/dom-utilities": {"version": "2.2.14", "resolved": "https://registry.npmjs.org/@fluentui/dom-utilities/-/dom-utilities-2.2.14.tgz", "integrity": "sha512-+4DVm5sNfJh+l8fM+7ylpOkGNZkNr4X1z1uKQPzRJ1PRhlnvc6vLpWNNicGwpjTbgufSrVtGKXwP5sf++r81lg==", "requires": {"@fluentui/set-version": "^8.2.14", "tslib": "^2.1.0"}}, "@fluentui/font-icons-mdl2": {"version": "8.5.31", "resolved": "https://registry.npmjs.org/@fluentui/font-icons-mdl2/-/font-icons-mdl2-8.5.31.tgz", "integrity": "sha512-jioHZ9XUfR9vUT5XnxdCrJ+hoC9TpYim+4YdtlUE/euI8kdW1tDZ5zqlSNk1GLDR34n03R09yWj5gVDCcMJbyQ==", "requires": {"@fluentui/set-version": "^8.2.14", "@fluentui/style-utilities": "^8.10.2", "@fluentui/utilities": "^8.13.24", "tslib": "^2.1.0"}}, "@fluentui/foundation-legacy": {"version": "8.2.51", "resolved": "https://registry.npmjs.org/@fluentui/foundation-legacy/-/foundation-legacy-8.2.51.tgz", "integrity": "sha512-z/jrp1imV66/D2MGpN/55LGk/Istymk5tN+XUFHDENDi+9zyb2MgSxFshp774DJIrg3vVlyuS8oo+dBuTM3UbQ==", "requires": {"@fluentui/merge-styles": "^8.5.15", "@fluentui/set-version": "^8.2.14", "@fluentui/style-utilities": "^8.10.2", "@fluentui/utilities": "^8.13.24", "tslib": "^2.1.0"}}, "@fluentui/keyboard-key": {"version": "0.4.14", "resolved": "https://registry.npmjs.org/@fluentui/keyboard-key/-/keyboard-key-0.4.14.tgz", "integrity": "sha512-XzZHcyFEM20H23h3i15UpkHi2AhRBriXPGAHq0Jm98TKFppXehedjjEFuUsh+CyU5JKBhDalWp8TAQ1ArpNzow==", "requires": {"tslib": "^2.1.0"}}, "@fluentui/keyboard-keys": {"version": "9.0.7", "resolved": "https://registry.npmjs.org/@fluentui/keyboard-keys/-/keyboard-keys-9.0.7.tgz", "integrity": "sha512-vaQ+lOveQTdoXJYqDQXWb30udSfTVcIuKk1rV0X0eGAgcHeSDeP1HxMy+OgHOQZH3OiBH4ZYeWxb+tmfiDiygQ==", "requires": {"@swc/helpers": "^0.5.1"}}, "@fluentui/merge-styles": {"version": "8.5.15", "resolved": "https://registry.npmjs.org/@fluentui/merge-styles/-/merge-styles-8.5.15.tgz", "integrity": "sha512-4CdKwo4k1Un2QLulpSVIz/KMgLNBMgin4NPyapmKDMVuO1OOxJUqfocubRGNO5x9mKgAMMYwBKGO9i0uxMMpJw==", "requires": {"@fluentui/set-version": "^8.2.14", "tslib": "^2.1.0"}}, "@fluentui/priority-overflow": {"version": "9.1.11", "resolved": "https://registry.npmjs.org/@fluentui/priority-overflow/-/priority-overflow-9.1.11.tgz", "integrity": "sha512-sdrpavvKX2kepQ1d6IaI3ObLq5SAQBPRHPGx2+wiMWL7cEx9vGGM0fmeicl3soqqmM5uwCmWnZk9QZv9XOY98w==", "requires": {"@swc/helpers": "^0.5.1"}}, "@fluentui/react": {"version": "8.114.4", "resolved": "https://registry.npmjs.org/@fluentui/react/-/react-8.114.4.tgz", "integrity": "sha512-dVpfFSpWUxdyqWlCVSXX5d34S760h4MaQjGR2/TPavtcJRRpJDHbBN2Hn7s4riA6YX5N7bTdN372UvIVbBbzuw==", "requires": {"@fluentui/date-time-utilities": "^8.5.16", "@fluentui/font-icons-mdl2": "^8.5.31", "@fluentui/foundation-legacy": "^8.2.51", "@fluentui/merge-styles": "^8.5.15", "@fluentui/react-focus": "^8.8.38", "@fluentui/react-hooks": "^8.6.36", "@fluentui/react-portal-compat-context": "^9.0.11", "@fluentui/react-window-provider": "^2.2.18", "@fluentui/set-version": "^8.2.14", "@fluentui/style-utilities": "^8.10.2", "@fluentui/theme": "^2.6.41", "@fluentui/utilities": "^8.13.24", "@microsoft/load-themed-styles": "^1.10.26", "tslib": "^2.1.0"}}, "@fluentui/react-accordion": {"version": "9.3.38", "resolved": "https://registry.npmjs.org/@fluentui/react-accordion/-/react-accordion-9.3.38.tgz", "integrity": "sha512-BB8d9+Jr0v4SW58OJTIyvsxhA/iOBbvIkQZlVHKqt4tL8dHOIFPrApw5WqQqaSYJsEwt4HxmlNU4Dv8qRughbg==", "requires": {"@fluentui/react-aria": "^9.7.3", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-alert": {"version": "9.0.0-beta.104", "resolved": "https://registry.npmjs.org/@fluentui/react-alert/-/react-alert-9.0.0-beta.104.tgz", "integrity": "sha512-Z8BGSyzEKok5wlJF2cUc8GUj2q+c1D+119YF0WtHLiieh7pwOHjBcDJOHqnaVnQNbhetIA3NUht2z0e1wgOK5w==", "requires": {"@fluentui/react-avatar": "^9.6.9", "@fluentui/react-button": "^9.3.65", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-aria": {"version": "9.7.3", "resolved": "https://registry.npmjs.org/@fluentui/react-aria/-/react-aria-9.7.3.tgz", "integrity": "sha512-YwyPNEcBDCdY6YzhrIrtlSrLs2Le7X1jLq9em8OnqHeiO22dBmg5xlBJoAMwJ8awCpI9xhu1PhU/2VJY4YqNuA==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-utilities": "^9.16.1", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-avatar": {"version": "9.6.9", "resolved": "https://registry.npmjs.org/@fluentui/react-avatar/-/react-avatar-9.6.9.tgz", "integrity": "sha512-3aZeUhGOg+UlHsp2x//G4VKRWKclcsZvX6L9UVnHsA/nQqRw7C5Bfo9iFNsEeJ3R5W5mFA6LyEFWedJ7QdAmdQ==", "requires": {"@fluentui/react-badge": "^9.2.22", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-popover": "^9.8.33", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-tooltip": "^9.4.11", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-badge": {"version": "9.2.22", "resolved": "https://registry.npmjs.org/@fluentui/react-badge/-/react-badge-9.2.22.tgz", "integrity": "sha512-zzimP5mZiiCOm8expUTzD6yvvKbnKq22PK/L6+oNpifrvQnDwJF/0nwXQVjA3+icNoYTaHe/q0fFivpXV+Js6g==", "requires": {"@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-breadcrumb": {"version": "9.0.11", "resolved": "https://registry.npmjs.org/@fluentui/react-breadcrumb/-/react-breadcrumb-9.0.11.tgz", "integrity": "sha512-L+AQqZz1gqkScD8IW1CjZWGNrDaHDc/gSv+PrvgSZeGDPibGj6TnLygJ7BKM+rQ+Hc2SbCogKbERpQZCbrSFvA==", "requires": {"@fluentui/react-aria": "^9.7.3", "@fluentui/react-button": "^9.3.65", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-link": "^9.2.7", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-button": {"version": "9.3.65", "resolved": "https://registry.npmjs.org/@fluentui/react-button/-/react-button-9.3.65.tgz", "integrity": "sha512-3VOt29AugkfR7VMnkKON449E7Sn/nvc6BBT4kJDGKQY+Nm5d2p9e4HmHp1UaM9zRPt47lagTY2WFJNrKKSe/BA==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-card": {"version": "9.0.64", "resolved": "https://registry.npmjs.org/@fluentui/react-card/-/react-card-9.0.64.tgz", "integrity": "sha512-TB/Zk+tLDUPNyAd2y8BvN0T2nroimtBOpB5GTK72E5sWPk0kaKIHwBEfXxNFGdGXcw0TAmVNqYi4ks37vh0Rgg==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-checkbox": {"version": "9.2.8", "resolved": "https://registry.npmjs.org/@fluentui/react-checkbox/-/react-checkbox-9.2.8.tgz", "integrity": "sha512-L4aWzeZdi98d0ZhgNPtxghfhasQv1qlxIRMaPxtwvk5TN6i9YmRF8vf5Pmf0PESjT+zp3VPcisHcIfcqG26SmQ==", "requires": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-combobox": {"version": "9.7.0", "resolved": "https://registry.npmjs.org/@fluentui/react-combobox/-/react-combobox-9.7.0.tgz", "integrity": "sha512-YmTdg04rvsg2+Dkw3ob+YLnS9rm3TLVMMNYTH0T64/FM3qirHntIXGbhMZXP5Cdo14gzQwr/e78NjBRKfYO4Wg==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-field": "^9.1.50", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-positioning": "^9.12.4", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-components": {"version": "9.45.0", "resolved": "https://registry.npmjs.org/@fluentui/react-components/-/react-components-9.45.0.tgz", "integrity": "sha512-Y+Laj1dvRcCp/nWT0DExRXoh7oKTX458g6oltrGjhIHikq4D6/kssK5tfhCyknPLwIlVSYi5J+G6L3NfvI8a8w==", "requires": {"@fluentui/react-accordion": "^9.3.38", "@fluentui/react-alert": "9.0.0-beta.104", "@fluentui/react-avatar": "^9.6.9", "@fluentui/react-badge": "^9.2.22", "@fluentui/react-breadcrumb": "^9.0.11", "@fluentui/react-button": "^9.3.65", "@fluentui/react-card": "^9.0.64", "@fluentui/react-checkbox": "^9.2.8", "@fluentui/react-combobox": "^9.7.0", "@fluentui/react-dialog": "^9.9.7", "@fluentui/react-divider": "^9.2.58", "@fluentui/react-drawer": "^9.1.1", "@fluentui/react-field": "^9.1.50", "@fluentui/react-image": "^9.1.55", "@fluentui/react-infobutton": "9.0.0-beta.88", "@fluentui/react-infolabel": "^9.0.16", "@fluentui/react-input": "^9.4.60", "@fluentui/react-label": "^9.1.58", "@fluentui/react-link": "^9.2.7", "@fluentui/react-menu": "^9.12.45", "@fluentui/react-message-bar": "^9.0.16", "@fluentui/react-overflow": "^9.1.8", "@fluentui/react-persona": "^9.2.68", "@fluentui/react-popover": "^9.8.33", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-positioning": "^9.12.4", "@fluentui/react-progress": "^9.1.60", "@fluentui/react-provider": "^9.13.8", "@fluentui/react-radio": "^9.2.3", "@fluentui/react-select": "^9.1.60", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-skeleton": "^9.0.48", "@fluentui/react-slider": "^9.1.65", "@fluentui/react-spinbutton": "^9.2.60", "@fluentui/react-spinner": "^9.3.38", "@fluentui/react-switch": "^9.1.65", "@fluentui/react-table": "^9.11.5", "@fluentui/react-tabs": "^9.4.6", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-tags": "^9.0.22", "@fluentui/react-text": "^9.4.7", "@fluentui/react-textarea": "^9.3.60", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-toast": "^9.3.27", "@fluentui/react-toolbar": "^9.1.66", "@fluentui/react-tooltip": "^9.4.11", "@fluentui/react-tree": "^9.4.25", "@fluentui/react-utilities": "^9.16.1", "@fluentui/react-virtualizer": "9.0.0-alpha.66", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-context-selector": {"version": "9.1.49", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.1.49.tgz", "integrity": "sha512-u4wRNfnyfuZDalVEESBPFQ0Ue4yYu+ozkPQvuEV6kriQGnAQQyyVbIidOCuP7Sja0nBwgM8eAzK0uX/slmmj3Q==", "requires": {"@fluentui/react-utilities": "^9.16.1", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-dialog": {"version": "9.9.7", "resolved": "https://registry.npmjs.org/@fluentui/react-dialog/-/react-dialog-9.9.7.tgz", "integrity": "sha512-5/6MeaHOYpx8Vt0auMJGLCjn6O1IYtl6IhwdwRNXL6AS1o4F24IKXdWZPtiHWuvzbuZAQd3+5nRDUE5KC9We6A==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1", "react-transition-group": "^4.4.1"}}, "@fluentui/react-divider": {"version": "9.2.58", "resolved": "https://registry.npmjs.org/@fluentui/react-divider/-/react-divider-9.2.58.tgz", "integrity": "sha512-y1ECy1zM4imKhpyOyUGugB+J30tfySO5hhrsIcpaiUQxRjE4IhZf2ZG6EqAQYLinJ+hV06yLZoazekljlvk6yw==", "requires": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-drawer": {"version": "9.1.1", "resolved": "https://registry.npmjs.org/@fluentui/react-drawer/-/react-drawer-9.1.1.tgz", "integrity": "sha512-3zvbbeaLLJZa4MXRpW8Ta4DFZ5457Tq9/4a0CqsIW/+8EuwtJwO+FB5a0DS6j0q6kN4mjkWF19OvzMkJsSTRVw==", "requires": {"@fluentui/react-dialog": "^9.9.7", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-motion-preview": "^0.5.10", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-field": {"version": "9.1.50", "resolved": "https://registry.npmjs.org/@fluentui/react-field/-/react-field-9.1.50.tgz", "integrity": "sha512-2mbx7YReMWvrgi3set9KepLLgMyNJ7StLu/HiHMM3jkcgPt3mGfwoJEsEKt+xd8eUAo4b82F7t+tHI4f9yzJaQ==", "requires": {"@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-focus": {"version": "8.8.38", "resolved": "https://registry.npmjs.org/@fluentui/react-focus/-/react-focus-8.8.38.tgz", "integrity": "sha512-vnsaY7hJSPIJBxm5Pj0FrcFDumV6kKgFVpsKsEKJzb1D88rDDLcmvz9jWUx68a3ru6idEbZYmyePGT1IiRsAug==", "requires": {"@fluentui/keyboard-key": "^0.4.14", "@fluentui/merge-styles": "^8.5.15", "@fluentui/set-version": "^8.2.14", "@fluentui/style-utilities": "^8.10.2", "@fluentui/utilities": "^8.13.24", "tslib": "^2.1.0"}}, "@fluentui/react-hooks": {"version": "8.6.36", "resolved": "https://registry.npmjs.org/@fluentui/react-hooks/-/react-hooks-8.6.36.tgz", "integrity": "sha512-kI0Z4Q4xHUs4SOmmI5n5OH5fPckqMSCovTRpiuxzCO2TNzLmfC861+nqf4Ygw/ChqNm2gWNZZfUADfnNAEsq+Q==", "requires": {"@fluentui/react-window-provider": "^2.2.18", "@fluentui/set-version": "^8.2.14", "@fluentui/utilities": "^8.13.24", "tslib": "^2.1.0"}}, "@fluentui/react-icons": {"version": "2.0.225", "resolved": "https://registry.npmjs.org/@fluentui/react-icons/-/react-icons-2.0.225.tgz", "integrity": "sha512-L9phN3bAMlZCa5+/ObGjIO+5GI8M50ym766sraSq92jaJwgAXrCJDLWuDGWZRGrC63DcagtR2culptj3q7gMMg==", "requires": {"@griffel/react": "^1.0.0", "tslib": "^2.1.0"}}, "@fluentui/react-image": {"version": "9.1.55", "resolved": "https://registry.npmjs.org/@fluentui/react-image/-/react-image-9.1.55.tgz", "integrity": "sha512-hYP61OWLuGSJNPOGJXtphbiDESfLB+/vsODKQsJhrDRJ2CSNMAfNznPHucqGRRN6AWQOI/BynJDS5F22Y//7CQ==", "requires": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-infobutton": {"version": "9.0.0-beta.88", "resolved": "https://registry.npmjs.org/@fluentui/react-infobutton/-/react-infobutton-9.0.0-beta.88.tgz", "integrity": "sha512-NVZyfrLtoFNu7cGkp2ORWsxJiCk1JgN4CVBDj03QSIh14EsPMwphYgDwfQ8TZOF2Nub0DGtC7/tF8IUlb/aP6g==", "requires": {"@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-popover": "^9.8.33", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-infolabel": {"version": "9.0.16", "resolved": "https://registry.npmjs.org/@fluentui/react-infolabel/-/react-infolabel-9.0.16.tgz", "integrity": "sha512-UCY+2vB4vOn0LfVhbgkyNG0EiuKIe0PdxEAtLU2PqosHLkaLKnYDKJdiIS/oaFmyNtGHmMxRkigvZpZ7h74f9g==", "requires": {"@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-popover": "^9.8.33", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-input": {"version": "9.4.60", "resolved": "https://registry.npmjs.org/@fluentui/react-input/-/react-input-9.4.60.tgz", "integrity": "sha512-kuk24K0X0gckTCssXoiWvZsTFVpZJv+WPl2fkjxeffzmFfBZtJUFQkXeC4/hcAg+aScjZnEtqjHjwDEbjZqkeA==", "requires": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-jsx-runtime": {"version": "9.0.27", "resolved": "https://registry.npmjs.org/@fluentui/react-jsx-runtime/-/react-jsx-runtime-9.0.27.tgz", "integrity": "sha512-9wxsWxVI7RLXsdK+7lzp7TK0FJKnrrj+Igxn0prqAvXdBRiFcuycoCJaHzC4Ka+Hsiol8NQg6xaIR59a28lmyQ==", "requires": {"@fluentui/react-utilities": "^9.16.1", "@swc/helpers": "^0.5.1", "react-is": "^17.0.2"}}, "@fluentui/react-label": {"version": "9.1.58", "resolved": "https://registry.npmjs.org/@fluentui/react-label/-/react-label-9.1.58.tgz", "integrity": "sha512-0ouSMop4vpXJzMvAyfmIr3TgDM/W1k+GFm8ZPD5fDQCopSJ+h3kvUZg5pqaXpBwamvZ16+qRARfTNITp2U7Rjw==", "requires": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-link": {"version": "9.2.7", "resolved": "https://registry.npmjs.org/@fluentui/react-link/-/react-link-9.2.7.tgz", "integrity": "sha512-z4X9dcUc/7FlqDxbGKbOfWubru+QimtzgMtlVxZ30pkC959hfIbFpbBY6Me76UOuFiOZxUPdfyY/73ekhhhVxw==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-menu": {"version": "9.12.45", "resolved": "https://registry.npmjs.org/@fluentui/react-menu/-/react-menu-9.12.45.tgz", "integrity": "sha512-qhpmuvAB4DUmmC5lNMakVvZjTdj/GZnH6WctNGZp94iCZLhcnIQcM9l0PvRpUpU1v3irXRyE5QV+x+wXC0awTw==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-positioning": "^9.12.4", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-message-bar": {"version": "9.0.16", "resolved": "https://registry.npmjs.org/@fluentui/react-message-bar/-/react-message-bar-9.0.16.tgz", "integrity": "sha512-R1VnqcFwu0pM2Yk8rjkN48Lx/n44UFD13BuY8/JeEuU8XQ8hLnEBVtdHjzRPJk+iM5in2ScMMQj4Z0nWyCRM1Q==", "requires": {"@fluentui/react-button": "^9.3.65", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1", "react-transition-group": "^4.4.1"}}, "@fluentui/react-motion-preview": {"version": "0.5.10", "resolved": "https://registry.npmjs.org/@fluentui/react-motion-preview/-/react-motion-preview-0.5.10.tgz", "integrity": "sha512-6iwF3N4hB6IxCoFVusgA2mp6mrTknwcsVGNYEQw1YF5WgGOMF3M0N1xNpN61/SYziT6HSUaI38NaA7LI3Dp3Sw==", "requires": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-overflow": {"version": "9.1.8", "resolved": "https://registry.npmjs.org/@fluentui/react-overflow/-/react-overflow-9.1.8.tgz", "integrity": "sha512-W8L68+0bUtfGr72LRx+U05EZLO0E8VMfscDiNKiEjDrOqdBnqNAIDN86825wrN77HH2wvILN07EhPOauqzz8YQ==", "requires": {"@fluentui/priority-overflow": "^9.1.11", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-persona": {"version": "9.2.68", "resolved": "https://registry.npmjs.org/@fluentui/react-persona/-/react-persona-9.2.68.tgz", "integrity": "sha512-C<PERSON>tDiZ34GGaw7lZ85uHZOuYXzkY21VHN6cUlGY1TJn98+Xz+y7JoVLIG7KZHHp2JzmmjtwjvgnqAdOun5LrWig==", "requires": {"@fluentui/react-avatar": "^9.6.9", "@fluentui/react-badge": "^9.2.22", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-popover": {"version": "9.8.33", "resolved": "https://registry.npmjs.org/@fluentui/react-popover/-/react-popover-9.8.33.tgz", "integrity": "sha512-0yPX6KCdMEGmrvJnQles5iTKN0OZ2vNSPVdkbyEKIUKj5DrNK1cMZEV/7Tgrtn922fx3/74FLMqEpEDTdrvQ/Q==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-positioning": "^9.12.4", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-portal": {"version": "9.4.10", "resolved": "https://registry.npmjs.org/@fluentui/react-portal/-/react-portal-9.4.10.tgz", "integrity": "sha512-k8fTRv9wTPSPCuNBFE2HxIhXsVYoG6Azb6Ib2xaDK+nczoW2WbsmNmwBJGEGi8UKjIoQzV+95KsYQ9me+uqKPA==", "requires": {"@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1", "use-disposable": "^1.0.1"}}, "@fluentui/react-portal-compat-context": {"version": "9.0.11", "resolved": "https://registry.npmjs.org/@fluentui/react-portal-compat-context/-/react-portal-compat-context-9.0.11.tgz", "integrity": "sha512-ubvW/ej0O+Pago9GH3mPaxzUgsNnBoqvghNamWjyKvZIViyaXUG6+sgcAl721R+qGAFac+A20akI5qDJz/xtdg==", "requires": {"@swc/helpers": "^0.5.1"}}, "@fluentui/react-positioning": {"version": "9.12.4", "resolved": "https://registry.npmjs.org/@fluentui/react-positioning/-/react-positioning-9.12.4.tgz", "integrity": "sha512-qQAjHF/FJFs2TyK0x08t0iFtDQlGNGH0OFC3jrG1xIFEe3nFPoeYeNT3zxOmj+D7bvlcJTIITcoe++YQTnCf4w==", "requires": {"@floating-ui/devtools": "0.2.1", "@floating-ui/dom": "^1.2.0", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-progress": {"version": "9.1.60", "resolved": "https://registry.npmjs.org/@fluentui/react-progress/-/react-progress-9.1.60.tgz", "integrity": "sha512-9wC7lWdo3S8rhxKWlIhcYAzsZNw+rL2HvNJTvEvFxXcOG7nJxP/3mGclV/jCCwDoPDnt9BT+40pGK84eD0BNIA==", "requires": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-provider": {"version": "9.13.8", "resolved": "https://registry.npmjs.org/@fluentui/react-provider/-/react-provider-9.13.8.tgz", "integrity": "sha512-FCvDMjs/BNAcqJuHU+kN/lqLB2RDQ/LQo29ltfLKFlTR1nTUNJvPMOVhjj6eEt+t81628LOYhbbaXOj9rYtfGg==", "requires": {"@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/core": "^1.14.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-radio": {"version": "9.2.3", "resolved": "https://registry.npmjs.org/@fluentui/react-radio/-/react-radio-9.2.3.tgz", "integrity": "sha512-8eKeUL0ZNr792Q6NGWPp7dpOV2IFcjAQ2oWR2/bruQVu8LMzYYKe2o6pQWdCag6UGPZuszkms9Xl7zPdDQBUdA==", "requires": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-select": {"version": "9.1.60", "resolved": "https://registry.npmjs.org/@fluentui/react-select/-/react-select-9.1.60.tgz", "integrity": "sha512-4HfRRTlGStOgtO00RY6jmOwz6MXnoa9gtjkV7StLmJZ2U5NTjVUrnp2dP1Vjb6hO13xaihWGEYyYKnsQ3R7kIw==", "requires": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-shared-contexts": {"version": "9.14.0", "resolved": "https://registry.npmjs.org/@fluentui/react-shared-contexts/-/react-shared-contexts-9.14.0.tgz", "integrity": "sha512-P9yhg31WYfB1W66/gD3+qVCLBsyIEcOzQvKVaIQvd9UhF67lNW4kMXUB6YVOk5PV0Og4hXnkH/vuHl7YMD9RHw==", "requires": {"@fluentui/react-theme": "^9.1.16", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-skeleton": {"version": "9.0.48", "resolved": "https://registry.npmjs.org/@fluentui/react-skeleton/-/react-skeleton-9.0.48.tgz", "integrity": "sha512-P0Rw5hIOn5CrZIWg7nVoK3gamxFhZI80KcRVaWap4O3gLo5C8nKHJWOtyBQZ5WKH+S6hoEGZ2USL6CoyXslxeQ==", "requires": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-slider": {"version": "9.1.65", "resolved": "https://registry.npmjs.org/@fluentui/react-slider/-/react-slider-9.1.65.tgz", "integrity": "sha512-7kuJMIojxCmNOuiRmQwh9iiXx8zwxkrgvsWmReRIBX0WB6w1VqMcuuikq2Z2ISgNPmepCX8W+qDfx8Ne4F/HtQ==", "requires": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-spinbutton": {"version": "9.2.60", "resolved": "https://registry.npmjs.org/@fluentui/react-spinbutton/-/react-spinbutton-9.2.60.tgz", "integrity": "sha512-0IIxEH0CTf4fNMoyvMa37bc63+0ZlznlsNy8lF3hujAT8Z9sUKVMH68e6tGUuXGJIkCUyDKU8HA+9FF2DyPvNA==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-field": "^9.1.50", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-spinner": {"version": "9.3.38", "resolved": "https://registry.npmjs.org/@fluentui/react-spinner/-/react-spinner-9.3.38.tgz", "integrity": "sha512-dPJr7/rgU2Qe/K2BciJTAEwEd0ytGpCw3VOVyK2T25w7Jw5RAHmgP+mbw+7se44Mr6sd1LH76mh5sfmQ3tODgw==", "requires": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-switch": {"version": "9.1.65", "resolved": "https://registry.npmjs.org/@fluentui/react-switch/-/react-switch-9.1.65.tgz", "integrity": "sha512-P0DwogD6hZJ3O005zCFPDoFXuzkrpKMrAeQGh9X0fqFP5JyHXVCgAAZQOLcphbbT9QukoEF5irN2Z4L9gBn57A==", "requires": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-table": {"version": "9.11.5", "resolved": "https://registry.npmjs.org/@fluentui/react-table/-/react-table-9.11.5.tgz", "integrity": "sha512-roQ<PERSON><PERSON>tl1aqXlachS2oTraVE45x3KdDrX0KyQGCdcQRxNprXJW6dIK9QjlbAL6yAsAMDafmFA4y9uRxl408dQ==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-avatar": "^9.6.9", "@fluentui/react-checkbox": "^9.2.8", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-radio": "^9.2.3", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-tabs": {"version": "9.4.6", "resolved": "https://registry.npmjs.org/@fluentui/react-tabs/-/react-tabs-9.4.6.tgz", "integrity": "sha512-LQvibLeJFyqKKiOjZUkRvbfLtsVosUhNUdh1SCQUPxQVpEPSK6XgwK0A1+jjoVhKn+PAJakxRINgnvqQD8pQBA==", "requires": {"@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-tabster": {"version": "9.17.3", "resolved": "https://registry.npmjs.org/@fluentui/react-tabster/-/react-tabster-9.17.3.tgz", "integrity": "sha512-cFcUYrkGW15w5yXzCPTTVG/7x5kNXxnhQXuh8SPyCc9JZeG7XI3+hy1T37PsXGxNS4KN9ePHkBHzgDfYO4gzYQ==", "requires": {"@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1", "keyborg": "^2.3.0", "tabster": "^5.0.1"}}, "@fluentui/react-tags": {"version": "9.0.22", "resolved": "https://registry.npmjs.org/@fluentui/react-tags/-/react-tags-9.0.22.tgz", "integrity": "sha512-gQIOCVu3HIfGjtAmwOnwBEnTsNyRBU8Pvs6EugpUyyqkRjzbm5TnL3LtiUy4f6/+NuaRqcYAvhwpdUhrlciwcA==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-avatar": "^9.6.9", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-text": {"version": "9.4.7", "resolved": "https://registry.npmjs.org/@fluentui/react-text/-/react-text-9.4.7.tgz", "integrity": "sha512-c6uJ98B35L8sviYxhQj1i+LW+HVNDdco2ImS9VLv/Duo4HiYs1G2y1YhtBDDiGxLe2moIvfg9ajDzMZV29aXFw==", "requires": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-textarea": {"version": "9.3.60", "resolved": "https://registry.npmjs.org/@fluentui/react-textarea/-/react-textarea-9.3.60.tgz", "integrity": "sha512-wH4MBWT4EOgNH9FXTjcgH34oANUaoduhmVjffnxaPl3R767Ak0fZPG7kky7yrLMjTDUSwILsEj/q+hsN6o+7Ag==", "requires": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-theme": {"version": "9.1.16", "resolved": "https://registry.npmjs.org/@fluentui/react-theme/-/react-theme-9.1.16.tgz", "integrity": "sha512-QK2dGE5aQXN1UGdiEmGKpYGP3tHXIchLvFf8DEEOWnF4XBc9SiEPNFYkvLMJjHxZmDz4D670rsOPe0r5jFDEKQ==", "requires": {"@fluentui/tokens": "1.0.0-alpha.13", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-toast": {"version": "9.3.27", "resolved": "https://registry.npmjs.org/@fluentui/react-toast/-/react-toast-9.3.27.tgz", "integrity": "sha512-DbRAYyL5Bd/pcFiGHPpK+rQMyc4LBll9YBy496l97dGDO2HmqFuiwP74V1KznxLcr4inCNWwThIJws5VLFsJLg==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1", "react-transition-group": "^4.4.1"}}, "@fluentui/react-toolbar": {"version": "9.1.66", "resolved": "https://registry.npmjs.org/@fluentui/react-toolbar/-/react-toolbar-9.1.66.tgz", "integrity": "sha512-ooNTp1R5MBZwiVK8fiJu29gE48vUx4NbXdwB2yHcCprasG3asjuoKQfOYM4+1NfFA0DetVrbK8L46IBeZyeBvA==", "requires": {"@fluentui/react-button": "^9.3.65", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-divider": "^9.2.58", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-radio": "^9.2.3", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-tooltip": {"version": "9.4.11", "resolved": "https://registry.npmjs.org/@fluentui/react-tooltip/-/react-tooltip-9.4.11.tgz", "integrity": "sha512-HXm8yYuAHJuczeFExco0WQSjO3DzDj5AJxqICHF8qtbtihUKfWpPnKM1qQWR+yJR2zc2jzvOEIzZXEkxSG+fSg==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-positioning": "^9.12.4", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-tree": {"version": "9.4.25", "resolved": "https://registry.npmjs.org/@fluentui/react-tree/-/react-tree-9.4.25.tgz", "integrity": "sha512-7IMqnOiNFMRuPujnbxJUYD8AEh0z1OGXkdNkAeLyj3pkwuvQs9+TbaNtv5Z372YN+kwYF4EYalYcPuNsRlx7cQ==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-avatar": "^9.6.9", "@fluentui/react-button": "^9.3.65", "@fluentui/react-checkbox": "^9.2.8", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-radio": "^9.2.3", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-utilities": {"version": "9.16.1", "resolved": "https://registry.npmjs.org/@fluentui/react-utilities/-/react-utilities-9.16.1.tgz", "integrity": "sha512-2wdwmgTFcVy14ZLbRNJ8Q6dCCBLekkJ8Znnok68gKRLDcwpPT3UjSraoU+DGjOA5BMfPppZBU8Yb5GqdIfd48g==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-shared-contexts": "^9.14.0", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-virtualizer": {"version": "9.0.0-alpha.66", "resolved": "https://registry.npmjs.org/@fluentui/react-virtualizer/-/react-virtualizer-9.0.0-alpha.66.tgz", "integrity": "sha512-x/ZOAIAwctt7pvOBIzS4iZGU0ahiPhQFS7iAHksFkF9LimneaV92A/02dW0Cy4v7dv9wZNoosQwhS05Yx3DVDQ==", "requires": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-window-provider": {"version": "2.2.18", "resolved": "https://registry.npmjs.org/@fluentui/react-window-provider/-/react-window-provider-2.2.18.tgz", "integrity": "sha512-nBKqxd0P8NmIR0qzFvka1urE2LVbUm6cse1I1T7TcOVNYa5jDf5BrO06+JRZfwbn00IJqOnIVoP0qONqceypWQ==", "requires": {"@fluentui/set-version": "^8.2.14", "tslib": "^2.1.0"}}, "@fluentui/set-version": {"version": "8.2.14", "resolved": "https://registry.npmjs.org/@fluentui/set-version/-/set-version-8.2.14.tgz", "integrity": "sha512-f/QWJnSeyfAjGAqq57yjMb6a5ejPlwfzdExPmzFBuEOuupi8hHbV8Yno12XJcTW4I0KXEQGw+PUaM1aOf/j7jw==", "requires": {"tslib": "^2.1.0"}}, "@fluentui/style-utilities": {"version": "8.10.2", "resolved": "https://registry.npmjs.org/@fluentui/style-utilities/-/style-utilities-8.10.2.tgz", "integrity": "sha512-ocELtMb/85nBa3rSfiAIwYx6TydN+3rQqv1P0H/L7etYNNtxOfS86JSWfn8zAsHMejbwUKJ1ZsIKs47c598XGQ==", "requires": {"@fluentui/merge-styles": "^8.5.15", "@fluentui/set-version": "^8.2.14", "@fluentui/theme": "^2.6.41", "@fluentui/utilities": "^8.13.24", "@microsoft/load-themed-styles": "^1.10.26", "tslib": "^2.1.0"}}, "@fluentui/theme": {"version": "2.6.41", "resolved": "https://registry.npmjs.org/@fluentui/theme/-/theme-2.6.41.tgz", "integrity": "sha512-h9RguEzqzJ0+59ys5Kkp7JtsjhDUxBLmQunu5rpHp5Mp788OtEjI/n1a9FIcOAL/priPSQwXN7RbuDpeP7+aSw==", "requires": {"@fluentui/merge-styles": "^8.5.15", "@fluentui/set-version": "^8.2.14", "@fluentui/utilities": "^8.13.24", "tslib": "^2.1.0"}}, "@fluentui/tokens": {"version": "1.0.0-alpha.13", "resolved": "https://registry.npmjs.org/@fluentui/tokens/-/tokens-1.0.0-alpha.13.tgz", "integrity": "sha512-IzYysTTBkAH7tQZxYKpzhxYnTJkvwXhjhTOpmERgnqTFifHTP8/vaQjJAAm7dI/9zlDx1oN+y/I+KzL9bDLHZQ==", "requires": {"@swc/helpers": "^0.5.1"}}, "@fluentui/utilities": {"version": "8.13.24", "resolved": "https://registry.npmjs.org/@fluentui/utilities/-/utilities-8.13.24.tgz", "integrity": "sha512-/jo6hWCzTGCx06l2baAMwsjjBZ/dyMouls53uNaQLUGUUhUwXh/DcDDXMqLRJB3MaH9zvgfvRw61iKmm2s9fIA==", "requires": {"@fluentui/dom-utilities": "^2.2.14", "@fluentui/merge-styles": "^8.5.15", "@fluentui/set-version": "^8.2.14", "tslib": "^2.1.0"}}, "@griffel/core": {"version": "1.15.2", "resolved": "https://registry.npmjs.org/@griffel/core/-/core-1.15.2.tgz", "integrity": "sha512-RlsIXoSS3gaYykUgxFpwKAs/DV9cRUKp3CW1kt3iPAtsDTWn/o+8bT1jvBws/tMM2GBu/Uc0EkaIzUPqD7uA+Q==", "requires": {"@emotion/hash": "^0.9.0", "@griffel/style-types": "^1.0.3", "csstype": "^3.1.3", "rtl-css-js": "^1.16.1", "stylis": "^4.2.0", "tslib": "^2.1.0"}}, "@griffel/react": {"version": "1.5.20", "resolved": "https://registry.npmjs.org/@griffel/react/-/react-1.5.20.tgz", "integrity": "sha512-1P2yaPctENFSCwyPIYXBmgpNH68c0lc/jwSzPij1QATHDK1AASKuSeq6hW108I67RKjhRyHCcALshdZ3GcQXSg==", "requires": {"@griffel/core": "^1.15.2", "tslib": "^2.1.0"}}, "@griffel/style-types": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/@griffel/style-types/-/style-types-1.0.3.tgz", "integrity": "sha512-AzbbYV/EobNIBtfMtyu2edFin895gjVxtu1nsRhTETUAIb0/LCZoue3Jd/kFLuPwe95rv5WRUBiQpVwJsrrFcw==", "requires": {"csstype": "^3.1.3"}}, "@jridgewell/gen-mapping": {"version": "0.3.8", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz", "integrity": "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==", "dev": true, "requires": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}}, "@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==", "dev": true}, "@jridgewell/set-array": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz", "integrity": "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==", "dev": true}, "@jridgewell/sourcemap-codec": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "integrity": "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==", "dev": true}, "@jridgewell/trace-mapping": {"version": "0.3.25", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "integrity": "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==", "dev": true, "requires": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "@microsoft/load-themed-styles": {"version": "1.10.295", "resolved": "https://registry.npmjs.org/@microsoft/load-themed-styles/-/load-themed-styles-1.10.295.tgz", "integrity": "sha512-W+IzEBw8a6LOOfRJM02dTT7BDZijxm+Z7lhtOAz1+y9vQm1Kdz9jlAO+qCEKsfxtUOmKilW8DIRqFw2aUgKeGg=="}, "@react-spring/animated": {"version": "9.7.3", "resolved": "https://registry.npmjs.org/@react-spring/animated/-/animated-9.7.3.tgz", "integrity": "sha512-5CWeNJt9pNgyvuSzQH+uy2pvTg8Y4/OisoscZIR8/ZNLIOI+CatFBhGZpDGTF/OzdNFsAoGk3wiUYTwoJ0YIvw==", "requires": {"@react-spring/shared": "~9.7.3", "@react-spring/types": "~9.7.3"}}, "@react-spring/core": {"version": "9.7.3", "resolved": "https://registry.npmjs.org/@react-spring/core/-/core-9.7.3.tgz", "integrity": "sha512-IqFdPVf3ZOC1Cx7+M0cXf4odNLxDC+n7IN3MDcVCTIOSBfqEcBebSv+vlY5AhM0zw05PDbjKrNmBpzv/AqpjnQ==", "requires": {"@react-spring/animated": "~9.7.3", "@react-spring/shared": "~9.7.3", "@react-spring/types": "~9.7.3"}}, "@react-spring/shared": {"version": "9.7.3", "resolved": "https://registry.npmjs.org/@react-spring/shared/-/shared-9.7.3.tgz", "integrity": "sha512-NEopD+9S5xYyQ0pGtioacLhL2luflh6HACSSDUZOwLHoxA5eku1UPuqcJqjwSD6luKjjLfiLOspxo43FUHKKSA==", "requires": {"@react-spring/types": "~9.7.3"}}, "@react-spring/types": {"version": "9.7.3", "resolved": "https://registry.npmjs.org/@react-spring/types/-/types-9.7.3.tgz", "integrity": "sha512-Kpx/fQ/ZFX31OtlqVEFfgaD1ACzul4NksrvIgYfIFq9JpDHFwQkMVZ10tbo0FU/grje4rcL4EIrjekl3kYwgWw=="}, "@react-spring/web": {"version": "9.7.3", "resolved": "https://registry.npmjs.org/@react-spring/web/-/web-9.7.3.tgz", "integrity": "sha512-BXt6BpS9aJL/QdVqEIX9YoUy8CE6TJrU0mNCqSoxdXlIeNcEBWOfIyE6B14ENNsyQKS3wOWkiJfco0tCr/9tUg==", "requires": {"@react-spring/animated": "~9.7.3", "@react-spring/core": "~9.7.3", "@react-spring/shared": "~9.7.3", "@react-spring/types": "~9.7.3"}}, "@remix-run/router": {"version": "1.14.2", "resolved": "https://registry.npmjs.org/@remix-run/router/-/router-1.14.2.tgz", "integrity": "sha512-ACXpdMM9hmKZww21yEqWwiLws/UPLhNKvimN8RrYSqPSvB3ov7sLvAcfvaxePeLvccTQKGdkDIhLYApZVDFuKg=="}, "@rollup/plugin-typescript": {"version": "11.1.3", "resolved": "https://registry.npmjs.org/@rollup/plugin-typescript/-/plugin-typescript-11.1.3.tgz", "integrity": "sha512-8o6cNgN44kQBcpsUJTbTXMTtb87oR1O0zgP3Dxm71hrNgparap3VujgofEilTYJo+ivf2ke6uy3/E5QEaiRlDA==", "requires": {"@rollup/pluginutils": "^5.0.1", "resolve": "^1.22.1"}}, "@rollup/pluginutils": {"version": "5.0.4", "resolved": "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-5.0.4.tgz", "integrity": "sha512-0KJnIoRI8A+a1dqOYLxH8vBf8bphDmty5QvIm2hqm7oFCFYKCAZWWd2hXgMibaPsNDhI0AtpYfQZJG47pt/k4g==", "requires": {"@types/estree": "^1.0.0", "estree-walker": "^2.0.2", "picomatch": "^2.3.1"}}, "@rollup/rollup-android-arm-eabi": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.34.9.tgz", "integrity": "sha512-qZdlImWXur0CFakn2BJ2znJOdqYZKiedEPEVNTBrpfPjc/YuTGcaYZcdmNFTkUj3DU0ZM/AElcM8Ybww3xVLzA==", "dev": true, "optional": true}, "@rollup/rollup-android-arm64": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.9.tgz", "integrity": "sha512-4KW7P53h6HtJf5Y608T1ISKvNIYLWRKMvfnG0c44M6In4DQVU58HZFEVhWINDZKp7FZps98G3gxwC1sb0wXUUg==", "dev": true, "optional": true}, "@rollup/rollup-darwin-arm64": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.34.9.tgz", "integrity": "sha512-0CY3/K54slrzLDjOA7TOjN1NuLKERBgk9nY5V34mhmuu673YNb+7ghaDUs6N0ujXR7fz5XaS5Aa6d2TNxZd0OQ==", "dev": true, "optional": true}, "@rollup/rollup-darwin-x64": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.34.9.tgz", "integrity": "sha512-eOojSEAi/acnsJVYRxnMkPFqcxSMFfrw7r2iD9Q32SGkb/Q9FpUY1UlAu1DH9T7j++gZ0lHjnm4OyH2vCI7l7Q==", "dev": true, "optional": true}, "@rollup/rollup-freebsd-arm64": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.34.9.tgz", "integrity": "sha512-2lzjQPJbN5UnHm7bHIUKFMulGTQwdvOkouJDpPysJS+QFBGDJqcfh+CxxtG23Ik/9tEvnebQiylYoazFMAgrYw==", "dev": true, "optional": true}, "@rollup/rollup-freebsd-x64": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.34.9.tgz", "integrity": "sha512-SLl0hi2Ah2H7xQYd6Qaiu01kFPzQ+hqvdYSoOtHYg/zCIFs6t8sV95kaoqjzjFwuYQLtOI0RZre/Ke0nPaQV+g==", "dev": true, "optional": true}, "@rollup/rollup-linux-arm-gnueabihf": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.34.9.tgz", "integrity": "sha512-88I+D3TeKItrw+Y/2ud4Tw0+3CxQ2kLgu3QvrogZ0OfkmX/DEppehus7L3TS2Q4lpB+hYyxhkQiYPJ6Mf5/dPg==", "dev": true, "optional": true}, "@rollup/rollup-linux-arm-musleabihf": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.34.9.tgz", "integrity": "sha512-3qyfWljSFHi9zH0KgtEPG4cBXHDFhwD8kwg6xLfHQ0IWuH9crp005GfoUUh/6w9/FWGBwEHg3lxK1iHRN1MFlA==", "dev": true, "optional": true}, "@rollup/rollup-linux-arm64-gnu": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.34.9.tgz", "integrity": "sha512-6TZjPHjKZUQKmVKMUowF3ewHxctrRR09eYyvT5eFv8w/fXarEra83A2mHTVJLA5xU91aCNOUnM+DWFMSbQ0Nxw==", "dev": true, "optional": true}, "@rollup/rollup-linux-arm64-musl": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.34.9.tgz", "integrity": "sha512-LD2fytxZJZ6xzOKnMbIpgzFOuIKlxVOpiMAXawsAZ2mHBPEYOnLRK5TTEsID6z4eM23DuO88X0Tq1mErHMVq0A==", "dev": true, "optional": true}, "@rollup/rollup-linux-loongarch64-gnu": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.34.9.tgz", "integrity": "sha512-dRAgTfDsn0TE0HI6cmo13hemKpVHOEyeciGtvlBTkpx/F65kTvShtY/EVyZEIfxFkV5JJTuQ9tP5HGBS0hfxIg==", "dev": true, "optional": true}, "@rollup/rollup-linux-powerpc64le-gnu": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.34.9.tgz", "integrity": "sha512-PHcNOAEhkoMSQtMf+rJofwisZqaU8iQ8EaSps58f5HYll9EAY5BSErCZ8qBDMVbq88h4UxaNPlbrKqfWP8RfJA==", "dev": true, "optional": true}, "@rollup/rollup-linux-riscv64-gnu": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.34.9.tgz", "integrity": "sha512-Z2i0Uy5G96KBYKjeQFKbbsB54xFOL5/y1P5wNBsbXB8yE+At3oh0DVMjQVzCJRJSfReiB2tX8T6HUFZ2k8iaKg==", "dev": true, "optional": true}, "@rollup/rollup-linux-s390x-gnu": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.34.9.tgz", "integrity": "sha512-U+5SwTMoeYXoDzJX5dhDTxRltSrIax8KWwfaaYcynuJw8mT33W7oOgz0a+AaXtGuvhzTr2tVKh5UO8GVANTxyQ==", "dev": true, "optional": true}, "@rollup/rollup-linux-x64-gnu": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.34.9.tgz", "integrity": "sha512-FwBHNSOjUTQLP4MG7y6rR6qbGw4MFeQnIBrMe161QGaQoBQLqSUEKlHIiVgF3g/mb3lxlxzJOpIBhaP+C+KP2A==", "dev": true, "optional": true}, "@rollup/rollup-linux-x64-musl": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.34.9.tgz", "integrity": "sha512-cYRpV4650z2I3/s6+5/LONkjIz8MBeqrk+vPXV10ORBnshpn8S32bPqQ2Utv39jCiDcO2eJTuSlPXpnvmaIgRA==", "dev": true, "optional": true}, "@rollup/rollup-win32-arm64-msvc": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.9.tgz", "integrity": "sha512-z4mQK9dAN6byRA/vsSgQiPeuO63wdiDxZ9yg9iyX2QTzKuQM7T4xlBoeUP/J8uiFkqxkcWndWi+W7bXdPbt27Q==", "dev": true, "optional": true}, "@rollup/rollup-win32-ia32-msvc": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.34.9.tgz", "integrity": "sha512-KB48mPtaoHy1AwDNkAJfHXvHp24H0ryZog28spEs0V48l3H1fr4i37tiyHsgKZJnCmvxsbATdZGBpbmxTE3a9w==", "dev": true, "optional": true}, "@rollup/rollup-win32-x64-msvc": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.34.9.tgz", "integrity": "sha512-AyleYRPU7+rgkMWbEh71fQlrzRfeP6SyMnRf9XX4fCdDPAJumdSBqYEcWPMzVQ4ScAl7E4oFfK0GUVn77xSwbw==", "dev": true, "optional": true}, "@swc/helpers": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.3.tgz", "integrity": "sha512-FaruWX6KdudYloq1AHD/4nU+UsMTdNE8CKyrseXWEcgjDAbvkwJg2QGPAnfIJLIWsjZOSPLOAykK6fuYp4vp4A==", "requires": {"tslib": "^2.4.0"}}, "@types/babel__core": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz", "integrity": "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==", "dev": true, "requires": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "@types/babel__generator": {"version": "7.6.8", "resolved": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.6.8.tgz", "integrity": "sha512-ASsj+tpEDsEiFr1arWrlN6V3mdfjRMZt6LtK/Vp/kreFLnr5QH5+DhvD5nINYZXzwJvXeGq+05iUXcAzVrqWtw==", "dev": true, "requires": {"@babel/types": "^7.0.0"}}, "@types/babel__template": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz", "integrity": "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==", "dev": true, "requires": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "@types/babel__traverse": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.5.tgz", "integrity": "sha512-WXCyOcRtH37HAUkpXhUduaxdm82b4GSlyTqajXviN4EfiuPgNYR109xMCKvpl6zPIpua0DGlMEDCq+g8EdoheQ==", "dev": true, "requires": {"@babel/types": "^7.20.7"}}, "@types/dompurify": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/@types/dompurify/-/dompurify-3.0.3.tgz", "integrity": "sha512-odiGr/9/qMqjcBOe5UhcNLOFHSYmKFOyr+bJ/Xu3Qp4k1pNPAlNLUVNNLcLfjQI7+W7ObX58EdD3H+3p3voOvA==", "dev": true, "requires": {"@types/trusted-types": "*"}}, "@types/estree": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/@types/estree/-/estree-1.0.6.tgz", "integrity": "sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw=="}, "@types/prop-types": {"version": "15.7.5", "resolved": "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.5.tgz", "integrity": "sha512-JCB8C6SnDoQf0cNycqd/35A7MjcnK+ZTqE7judS6o7utxUCg6imJg3QK2qzHKszlTjcj2cn+NwMB2i96ubpj7w=="}, "@types/react": {"version": "18.2.48", "resolved": "https://registry.npmjs.org/@types/react/-/react-18.2.48.tgz", "integrity": "sha512-qboRCl6Ie70DQQG9hhNREz81jqC1cs9EVNcjQ1AU+jH6NFfSAhVVbrrY/+nSF+Bsk4AOwm9Qa61InvMCyV+H3w==", "requires": {"@types/prop-types": "*", "@types/scheduler": "*", "csstype": "^3.0.2"}}, "@types/react-dom": {"version": "18.2.18", "resolved": "https://registry.npmjs.org/@types/react-dom/-/react-dom-18.2.18.tgz", "integrity": "sha512-TJxDm6OfAX2KJWJdMEVTwWke5Sc/E/RlnPGvGfS0W7+6ocy2xhDVQVh/KvC2Uf7kACs+gDytdusDSdWfWkaNzw==", "requires": {"@types/react": "*"}}, "@types/scheduler": {"version": "0.16.2", "resolved": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.2.tgz", "integrity": "sha512-hppQEBDmlwhFAXKJX2KnWLYu5yMfi91yazPb2l+lbJiwW+wdo1gNeRA+3RgNSO39WYX2euey41KEwnqesU2Jew=="}, "@types/trusted-types": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/@types/trusted-types/-/trusted-types-2.0.7.tgz", "integrity": "sha512-ScaPdn1dQczgbl0QFTeTOmVHFULt394XJgOQNoyVhZ6r2vLnMLJfBPd53SB52T/3G36VI1/g2MZaX0cwDuXsfw==", "devOptional": true}, "@vitejs/plugin-react": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.3.4.tgz", "integrity": "sha512-SCCPBJtYLdE8PX/7ZQAs1QAZ8Jqwih+0VBLum1EGqmCCQal+MIUqLCzj3ZUy8ufbC0cAM4LRlSTm7IQJwWT4ug==", "dev": true, "requires": {"@babel/core": "^7.26.0", "@babel/plugin-transform-react-jsx-self": "^7.25.9", "@babel/plugin-transform-react-jsx-source": "^7.25.9", "@types/babel__core": "^7.20.5", "react-refresh": "^0.14.2"}}, "browserslist": {"version": "4.24.4", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.24.4.tgz", "integrity": "sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A==", "dev": true, "requires": {"caniuse-lite": "^1.0.30001688", "electron-to-chromium": "^1.5.73", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.1"}}, "caniuse-lite": {"version": "1.0.30001702", "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001702.tgz", "integrity": "sha512-LoPe/D7zioC0REI5W73PeR1e1MLCipRGq/VkovJnd6Df+QVqT+vT33OXCp8QUd7kA7RZrHWxb1B36OQKI/0gOA==", "dev": true}, "convert-source-map": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz", "integrity": "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==", "dev": true}, "csstype": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz", "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw=="}, "debug": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz", "integrity": "sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==", "dev": true, "requires": {"ms": "^2.1.3"}}, "dom-helpers": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz", "integrity": "sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==", "requires": {"@babel/runtime": "^7.8.7", "csstype": "^3.0.2"}}, "dompurify": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/dompurify/-/dompurify-3.2.4.tgz", "integrity": "sha512-ysFSFEDVduQpyhzAob/kkuJjf5zWkZD8/A9ywSp1byueyuCfHamrCBa14/Oc2iiB0e51B+NpxSl5gmzn+Ms/mg==", "requires": {"@types/trusted-types": "^2.0.7"}}, "electron-to-chromium": {"version": "1.5.112", "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.112.tgz", "integrity": "sha512-oen93kVyqSb3l+ziUgzIOlWt/oOuy4zRmpwestMn4rhFWAoFJeFuCVte9F2fASjeZZo7l/Cif9TiyrdW4CwEMA==", "dev": true}, "esbuild": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/esbuild/-/esbuild-0.25.0.tgz", "integrity": "sha512-BXq5mqc8ltbaN34cDqWuYKyNhX8D/Z0J1xdtdQ8UcIIIyJyz+ZMKUt58tF3SrZ85jcfN/PZYhjR5uDQAYNVbuw==", "dev": true, "requires": {"@esbuild/aix-ppc64": "0.25.0", "@esbuild/android-arm": "0.25.0", "@esbuild/android-arm64": "0.25.0", "@esbuild/android-x64": "0.25.0", "@esbuild/darwin-arm64": "0.25.0", "@esbuild/darwin-x64": "0.25.0", "@esbuild/freebsd-arm64": "0.25.0", "@esbuild/freebsd-x64": "0.25.0", "@esbuild/linux-arm": "0.25.0", "@esbuild/linux-arm64": "0.25.0", "@esbuild/linux-ia32": "0.25.0", "@esbuild/linux-loong64": "0.25.0", "@esbuild/linux-mips64el": "0.25.0", "@esbuild/linux-ppc64": "0.25.0", "@esbuild/linux-riscv64": "0.25.0", "@esbuild/linux-s390x": "0.25.0", "@esbuild/linux-x64": "0.25.0", "@esbuild/netbsd-arm64": "0.25.0", "@esbuild/netbsd-x64": "0.25.0", "@esbuild/openbsd-arm64": "0.25.0", "@esbuild/openbsd-x64": "0.25.0", "@esbuild/sunos-x64": "0.25.0", "@esbuild/win32-arm64": "0.25.0", "@esbuild/win32-ia32": "0.25.0", "@esbuild/win32-x64": "0.25.0"}}, "escalade": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "dev": true}, "estree-walker": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz", "integrity": "sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w=="}, "frontend": {"version": "file:", "requires": {"@azure/msal-browser": "^3.1.0", "@azure/msal-react": "^2.0.4", "@fluentui/react": "^8.112.5", "@fluentui/react-components": "^9.37.3", "@fluentui/react-icons": "^2.0.221", "@react-spring/web": "^9.7.3", "@types/dompurify": "^3.0.3", "@types/react": "^18.2.34", "@types/react-dom": "^18.2.14", "@vitejs/plugin-react": "^4.3.4", "dompurify": "^3.2.4", "frontend": "file:", "ndjson-readablestream": "^1.0.7", "prettier": "^3.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "scheduler": "^0.20.2", "typescript": "^5.2.2", "vite": "^6.3.1"}, "dependencies": {"@ampproject/remapping": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz", "integrity": "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==", "dev": true, "requires": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}}, "@azure/msal-browser": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/@azure/msal-browser/-/msal-browser-3.2.0.tgz", "integrity": "sha512-le2qutddMiq0i3ErQaLKuwP1DpNgdd9iXPs3fSCsLuBrdGg9B4/j4ArCAHCwgxA82Ydj9BcqtMIL5BSWwU+P5A==", "requires": {"@azure/msal-common": "14.1.0"}}, "@azure/msal-common": {"version": "14.1.0", "resolved": "https://registry.npmjs.org/@azure/msal-common/-/msal-common-14.1.0.tgz", "integrity": "sha512-xphmhcfl5VL+uq5//VKMwQn+wfEZLMKNpFCcMi8Ur8ej5UT166g6chBsxgMzc9xo9Y24R9FB3m/tjDiV03xMIA=="}, "@azure/msal-react": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/@azure/msal-react/-/msal-react-2.0.4.tgz", "integrity": "sha512-BujRm5FBDWYXyr3pnmubS4dIqZMlurYVtV2AyztoeAFUd+nh3XQZD9knHBqTyu53IDjhCCvUPUke/jSkv5WGlg==", "requires": {"@rollup/plugin-typescript": "^11.1.0", "rollup": "^3.20.2"}}, "@babel/code-frame": {"version": "7.26.2", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.26.2.tgz", "integrity": "sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.25.9", "js-tokens": "^4.0.0", "picocolors": "^1.0.0"}}, "@babel/compat-data": {"version": "7.26.8", "resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.26.8.tgz", "integrity": "sha512-oH5UPLMWR3L2wEFLnFJ1TZXqHufiTKAiLfqw5zkhS4dKXLJ10yVztfil/twG8EDTA4F/tvVNw9nOl4ZMslB8rQ==", "dev": true}, "@babel/core": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.26.9.tgz", "integrity": "sha512-lWBYIrF7qK5+GjY5Uy+/hEgp8OJWOD/rpy74GplYRhEauvbHDeFB8t5hPOZxCZ0Oxf4Cc36tK51/l3ymJysrKw==", "dev": true, "requires": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.26.2", "@babel/generator": "^7.26.9", "@babel/helper-compilation-targets": "^7.26.5", "@babel/helper-module-transforms": "^7.26.0", "@babel/helpers": "^7.26.9", "@babel/parser": "^7.26.9", "@babel/template": "^7.26.9", "@babel/traverse": "^7.26.9", "@babel/types": "^7.26.9", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}}, "@babel/generator": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.26.9.tgz", "integrity": "sha512-kEWdzjOAUMW4hAyrzJ0ZaTOu9OmpyDIQicIh0zg0EEcEkYXZb2TjtBhnHi2ViX7PKwZqF4xwqfAm299/QMP3lg==", "dev": true, "requires": {"@babel/parser": "^7.26.9", "@babel/types": "^7.26.9", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-compilation-targets": {"version": "7.26.5", "resolved": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.26.5.tgz", "integrity": "sha512-IXuyn5EkouFJscIDuFF5EsiSolseme1s0CZB+QxVugqJLYmKdxI1VfIBOst0SUu4rnk2Z7kqTwmoO1lp3HIfnA==", "dev": true, "requires": {"@babel/compat-data": "^7.26.5", "@babel/helper-validator-option": "^7.25.9", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}}, "@babel/helper-module-imports": {"version": "7.25.9", "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.25.9.tgz", "integrity": "sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==", "dev": true, "requires": {"@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}}, "@babel/helper-module-transforms": {"version": "7.26.0", "resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.26.0.tgz", "integrity": "sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==", "dev": true, "requires": {"@babel/helper-module-imports": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/traverse": "^7.25.9"}}, "@babel/helper-plugin-utils": {"version": "7.26.5", "resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz", "integrity": "sha512-RS+jZcRdZdRFzMyr+wcsaqOmld1/EqTghfaBGQQd/WnRdzdlvSZ//kF7U8VQTxf1ynZ4cjUcYgjVGx13ewNPMg==", "dev": true}, "@babel/helper-string-parser": {"version": "7.25.9", "resolved": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.25.9.tgz", "integrity": "sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==", "dev": true}, "@babel/helper-validator-identifier": {"version": "7.25.9", "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.9.tgz", "integrity": "sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==", "dev": true}, "@babel/helper-validator-option": {"version": "7.25.9", "resolved": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.25.9.tgz", "integrity": "sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==", "dev": true}, "@babel/helpers": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.26.9.tgz", "integrity": "sha512-Mz/4+y8udxBKdmzt/UjPACs4G3j5SshJJEFFKxlCGPydG4JAHXxjWjAwjd09tf6oINvl1VfMJo+nB7H2YKQ0dA==", "dev": true, "requires": {"@babel/template": "^7.26.9", "@babel/types": "^7.26.9"}}, "@babel/parser": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.26.9.tgz", "integrity": "sha512-81NWa1njQblgZbQHxWHpxxCzNsa3ZwvFqpUg7P+NNUU6f3UU2jBEg4OlF/J6rl8+PQGh1q6/zWScd001YwcA5A==", "dev": true, "requires": {"@babel/types": "^7.26.9"}}, "@babel/plugin-transform-react-jsx-self": {"version": "7.25.9", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.25.9.tgz", "integrity": "sha512-y8quW6p0WHkEhmErnfe58r7x0A70uKphQm8Sp8cV7tjNQwK56sNVK0M73LK3WuYmsuyrftut4xAkjjgU0twaMg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.25.9"}}, "@babel/plugin-transform-react-jsx-source": {"version": "7.25.9", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.25.9.tgz", "integrity": "sha512-+iqjT8xmXhhYv4/uiYd8FNQsraMFZIfxVSqxxVSZP0WbbSAWvBXAul0m/zu+7Vv4O/3WtApy9pmaTMiumEZgfg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.25.9"}}, "@babel/runtime": {"version": "7.27.0", "resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.0.tgz", "integrity": "sha512-VtPOkrdPHZsKc/clNqyi9WUA8TINkZ4cGk63UUE3u4pmB2k+ZMQRDuIOagv8UVd6j7k0T3+RRIb7beKTebNbcw==", "requires": {"regenerator-runtime": "^0.14.0"}}, "@babel/template": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/template/-/template-7.26.9.tgz", "integrity": "sha512-qyRplbeIpNZhmzOysF/wFMuP9sctmh2cFzRAZOn1YapxBsE1i9bJIY586R/WBLfLcmcBlM8ROBiQURnnNy+zfA==", "dev": true, "requires": {"@babel/code-frame": "^7.26.2", "@babel/parser": "^7.26.9", "@babel/types": "^7.26.9"}}, "@babel/traverse": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.26.9.tgz", "integrity": "sha512-ZYW7L+pL8ahU5fXmNbPF+iZFHCv5scFak7MZ9bwaRPLUhHh7QQEMjZUg0HevihoqCM5iSYHN61EyCoZvqC+bxg==", "dev": true, "requires": {"@babel/code-frame": "^7.26.2", "@babel/generator": "^7.26.9", "@babel/parser": "^7.26.9", "@babel/template": "^7.26.9", "@babel/types": "^7.26.9", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.26.9", "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.26.9.tgz", "integrity": "sha512-Y3IR1cRnOxOCDvMmNiym7XpXQ93iGDDPHx+Zj+NM+rg0fBaShfQLkg+hKPaZCEvg5N/LeCo4+Rj/i3FuJsIQaw==", "dev": true, "requires": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}}, "@emotion/hash": {"version": "0.9.1", "resolved": "https://registry.npmjs.org/@emotion/hash/-/hash-0.9.1.tgz", "integrity": "sha512-gJB6HLm5rYwSLI6PQa+X1t5CFGrv1J1TWG+sOyMCeKz2ojaj6Fnl/rZEspogG+cvqbt4AE/2eIyD2QfLKTBNlQ=="}, "@esbuild/aix-ppc64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/aix-ppc64/-/aix-ppc64-0.25.0.tgz", "integrity": "sha512-O7vun9Sf8DFjH2UtqK8Ku3LkquL9SZL8OLY1T5NZkA34+wG3OQF7cl4Ql8vdNzM6fzBbYfLaiRLIOZ+2FOCgBQ==", "dev": true, "optional": true}, "@esbuild/android-arm": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.25.0.tgz", "integrity": "sha512-PTyWCYYiU0+1eJKmw21lWtC+d08JDZPQ5g+kFyxP0V+es6VPPSUhM6zk8iImp2jbV6GwjX4pap0JFbUQN65X1g==", "dev": true, "optional": true}, "@esbuild/android-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/android-arm64/-/android-arm64-0.25.0.tgz", "integrity": "sha512-grvv8WncGjDSyUBjN9yHXNt+cq0snxXbDxy5pJtzMKGmmpPxeAmAhWxXI+01lU5rwZomDgD3kJwulEnhTRUd6g==", "dev": true, "optional": true}, "@esbuild/android-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/android-x64/-/android-x64-0.25.0.tgz", "integrity": "sha512-m/ix7SfKG5buCnxasr52+LI78SQ+wgdENi9CqyCXwjVR2X4Jkz+BpC3le3AoBPYTC9NHklwngVXvbJ9/Akhrfg==", "dev": true, "optional": true}, "@esbuild/darwin-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/darwin-arm64/-/darwin-arm64-0.25.0.tgz", "integrity": "sha512-mVwdUb5SRkPayVadIOI78K7aAnPamoeFR2bT5nszFUZ9P8UpK4ratOdYbZZXYSqPKMHfS1wdHCJk1P1EZpRdvw==", "dev": true, "optional": true}, "@esbuild/darwin-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/darwin-x64/-/darwin-x64-0.25.0.tgz", "integrity": "sha512-DgDaYsPWFTS4S3nWpFcMn/33ZZwAAeAFKNHNa1QN0rI4pUjgqf0f7ONmXf6d22tqTY+H9FNdgeaAa+YIFUn2Rg==", "dev": true, "optional": true}, "@esbuild/freebsd-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.0.tgz", "integrity": "sha512-VN4ocxy6dxefN1MepBx/iD1dH5K8qNtNe227I0mnTRjry8tj5MRk4zprLEdG8WPyAPb93/e4pSgi1SoHdgOa4w==", "dev": true, "optional": true}, "@esbuild/freebsd-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.25.0.tgz", "integrity": "sha512-mrSgt7lCh07FY+hDD1TxiTyIHyttn6vnjesnPoVDNmDfOmggTLXRv8Id5fNZey1gl/V2dyVK1VXXqVsQIiAk+A==", "dev": true, "optional": true}, "@esbuild/linux-arm": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.25.0.tgz", "integrity": "sha512-vkB3IYj2IDo3g9xX7HqhPYxVkNQe8qTK55fraQyTzTX/fxaDtXiEnavv9geOsonh2Fd2RMB+i5cbhu2zMNWJwg==", "dev": true, "optional": true}, "@esbuild/linux-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-arm64/-/linux-arm64-0.25.0.tgz", "integrity": "sha512-9QAQjTWNDM/Vk2bgBl17yWuZxZNQIF0OUUuPZRKoDtqF2k4EtYbpyiG5/Dk7nqeK6kIJWPYldkOcBqjXjrUlmg==", "dev": true, "optional": true}, "@esbuild/linux-ia32": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-ia32/-/linux-ia32-0.25.0.tgz", "integrity": "sha512-43ET5bHbphBegyeqLb7I1eYn2P/JYGNmzzdidq/w0T8E2SsYL1U6un2NFROFRg1JZLTzdCoRomg8Rvf9M6W6Gg==", "dev": true, "optional": true}, "@esbuild/linux-loong64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-loong64/-/linux-loong64-0.25.0.tgz", "integrity": "sha512-fC95c/xyNFueMhClxJmeRIj2yrSMdDfmqJnyOY4ZqsALkDrrKJfIg5NTMSzVBr5YW1jf+l7/cndBfP3MSDpoHw==", "dev": true, "optional": true}, "@esbuild/linux-mips64el": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.25.0.tgz", "integrity": "sha512-nkAMFju7KDW73T1DdH7glcyIptm95a7Le8irTQNO/qtkoyypZAnjchQgooFUDQhNAy4iu08N79W4T4pMBwhPwQ==", "dev": true, "optional": true}, "@esbuild/linux-ppc64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-ppc64/-/linux-ppc64-0.25.0.tgz", "integrity": "sha512-NhyOejdhRGS8Iwv+KKR2zTq2PpysF9XqY+Zk77vQHqNbo/PwZCzB5/h7VGuREZm1fixhs4Q/qWRSi5zmAiO4Fw==", "dev": true, "optional": true}, "@esbuild/linux-riscv64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-riscv64/-/linux-riscv64-0.25.0.tgz", "integrity": "sha512-5S/rbP5OY+GHLC5qXp1y/Mx//e92L1YDqkiBbO9TQOvuFXM+iDqUNG5XopAnXoRH3FjIUDkeGcY1cgNvnXp/kA==", "dev": true, "optional": true}, "@esbuild/linux-s390x": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.25.0.tgz", "integrity": "sha512-XM2BFsEBz0Fw37V0zU4CXfcfuACMrppsMFKdYY2WuTS3yi8O1nFOhil/xhKTmE1nPmVyvQJjJivgDT+xh8pXJA==", "dev": true, "optional": true}, "@esbuild/linux-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-x64/-/linux-x64-0.25.0.tgz", "integrity": "sha512-9yl91rHw/cpwMCNytUDxwj2XjFpxML0y9HAOH9pNVQDpQrBxHy01Dx+vaMu0N1CKa/RzBD2hB4u//nfc+Sd3Cw==", "dev": true, "optional": true}, "@esbuild/netbsd-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.0.tgz", "integrity": "sha512-RuG4PSMPFfrkH6UwCAqBzauBWTygTvb1nxWasEJooGSJ/NwRw7b2HOwyRTQIU97Hq37l3npXoZGYMy3b3xYvPw==", "dev": true, "optional": true}, "@esbuild/netbsd-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.25.0.tgz", "integrity": "sha512-jl+qisSB5jk01N5f7sPCsBENCOlPiS/xptD5yxOx2oqQfyourJwIKLRA2yqWdifj3owQZCL2sn6o08dBzZGQzA==", "dev": true, "optional": true}, "@esbuild/openbsd-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.0.tgz", "integrity": "sha512-21sUNbq2r84YE+SJDfaQRvdgznTD8Xc0oc3p3iW/a1EVWeNj/SdUCbm5U0itZPQYRuRTW20fPMWMpcrciH2EJw==", "dev": true, "optional": true}, "@esbuild/openbsd-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/openbsd-x64/-/openbsd-x64-0.25.0.tgz", "integrity": "sha512-2gwwriSMPcCFRlPlKx3zLQhfN/2WjJ2NSlg5TKLQOJdV0mSxIcYNTMhk3H3ulL/cak+Xj0lY1Ym9ysDV1igceg==", "dev": true, "optional": true}, "@esbuild/sunos-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.25.0.tgz", "integrity": "sha512-bxI7ThgLzPrPz484/S9jLlvUAHYMzy6I0XiU1ZMeAEOBcS0VePBFxh1JjTQt3Xiat5b6Oh4x7UC7IwKQKIJRIg==", "dev": true, "optional": true}, "@esbuild/win32-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.25.0.tgz", "integrity": "sha512-ZUAc2YK6JW89xTbXvftxdnYy3m4iHIkDtK3CLce8wg8M2L+YZhIvO1DKpxrd0Yr59AeNNkTiic9YLf6FTtXWMw==", "dev": true, "optional": true}, "@esbuild/win32-ia32": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/win32-ia32/-/win32-ia32-0.25.0.tgz", "integrity": "sha512-eSNxISBu8XweVEWG31/JzjkIGbGIJN/TrRoiSVZwZ6pkC6VX4Im/WV2cz559/TXLcYbcrDN8JtKgd9DJVIo8GA==", "dev": true, "optional": true}, "@esbuild/win32-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.25.0.tgz", "integrity": "sha512-ZENoHJBxA20C2zFzh6AI4fT6RraMzjYw4xKWemRTRmRVtN9c5DcH9r/f2ihEkMjOW5eGgrwCslG/+Y/3bL+DHQ==", "dev": true, "optional": true}, "@floating-ui/core": {"version": "1.5.3", "resolved": "https://registry.npmjs.org/@floating-ui/core/-/core-1.5.3.tgz", "integrity": "sha512-O0WKDOo0yhJuugCx6trZQj5jVJ9yR0ystG2JaNAemYUWce+pmM6WUEFIibnWyEJKdrDxhm75NoSRME35FNaM/Q==", "requires": {"@floating-ui/utils": "^0.2.0"}}, "@floating-ui/devtools": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@floating-ui/devtools/-/devtools-0.2.1.tgz", "integrity": "sha512-8PHJLbD6VhBh+LJ1uty/Bz30qs02NXCE5u8WpOhSewlYXUWl03GNXknr9AS2yaAWJEQaY27x7eByJs44gODBcw==", "requires": {}}, "@floating-ui/dom": {"version": "1.5.4", "resolved": "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.5.4.tgz", "integrity": "sha512-jByEsHIY+eEdCjnTVu+E3ephzTOzkQ8hgUfGwos+bg7NlH33Zc5uO+QHz1mrQUOgIKKDD1RtS201P9NvAfq3XQ==", "requires": {"@floating-ui/core": "^1.5.3", "@floating-ui/utils": "^0.2.0"}}, "@floating-ui/utils": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.1.tgz", "integrity": "sha512-9TANp6GPoMtYzQdt54kfAyMmz1+osLlXdg2ENroU7zzrtflTLrrC/lgrIfaSe+Wu0b89GKccT7vxXA0MoAIO+Q=="}, "@fluentui/date-time-utilities": {"version": "8.5.16", "resolved": "https://registry.npmjs.org/@fluentui/date-time-utilities/-/date-time-utilities-8.5.16.tgz", "integrity": "sha512-l+mLfJ2VhdHjBpELLLPDaWgT7GMLynm2aqR7SttbEb6Jh7hc/7ck1MWm93RTb3gYVHYai8SENqimNcvIxHt/zg==", "requires": {"@fluentui/set-version": "^8.2.14", "tslib": "^2.1.0"}}, "@fluentui/dom-utilities": {"version": "2.2.14", "resolved": "https://registry.npmjs.org/@fluentui/dom-utilities/-/dom-utilities-2.2.14.tgz", "integrity": "sha512-+4DVm5sNfJh+l8fM+7ylpOkGNZkNr4X1z1uKQPzRJ1PRhlnvc6vLpWNNicGwpjTbgufSrVtGKXwP5sf++r81lg==", "requires": {"@fluentui/set-version": "^8.2.14", "tslib": "^2.1.0"}}, "@fluentui/font-icons-mdl2": {"version": "8.5.31", "resolved": "https://registry.npmjs.org/@fluentui/font-icons-mdl2/-/font-icons-mdl2-8.5.31.tgz", "integrity": "sha512-jioHZ9XUfR9vUT5XnxdCrJ+hoC9TpYim+4YdtlUE/euI8kdW1tDZ5zqlSNk1GLDR34n03R09yWj5gVDCcMJbyQ==", "requires": {"@fluentui/set-version": "^8.2.14", "@fluentui/style-utilities": "^8.10.2", "@fluentui/utilities": "^8.13.24", "tslib": "^2.1.0"}}, "@fluentui/foundation-legacy": {"version": "8.2.51", "resolved": "https://registry.npmjs.org/@fluentui/foundation-legacy/-/foundation-legacy-8.2.51.tgz", "integrity": "sha512-z/jrp1imV66/D2MGpN/55LGk/Istymk5tN+XUFHDENDi+9zyb2MgSxFshp774DJIrg3vVlyuS8oo+dBuTM3UbQ==", "requires": {"@fluentui/merge-styles": "^8.5.15", "@fluentui/set-version": "^8.2.14", "@fluentui/style-utilities": "^8.10.2", "@fluentui/utilities": "^8.13.24", "tslib": "^2.1.0"}}, "@fluentui/keyboard-key": {"version": "0.4.14", "resolved": "https://registry.npmjs.org/@fluentui/keyboard-key/-/keyboard-key-0.4.14.tgz", "integrity": "sha512-XzZHcyFEM20H23h3i15UpkHi2AhRBriXPGAHq0Jm98TKFppXehedjjEFuUsh+CyU5JKBhDalWp8TAQ1ArpNzow==", "requires": {"tslib": "^2.1.0"}}, "@fluentui/keyboard-keys": {"version": "9.0.7", "resolved": "https://registry.npmjs.org/@fluentui/keyboard-keys/-/keyboard-keys-9.0.7.tgz", "integrity": "sha512-vaQ+lOveQTdoXJYqDQXWb30udSfTVcIuKk1rV0X0eGAgcHeSDeP1HxMy+OgHOQZH3OiBH4ZYeWxb+tmfiDiygQ==", "requires": {"@swc/helpers": "^0.5.1"}}, "@fluentui/merge-styles": {"version": "8.5.15", "resolved": "https://registry.npmjs.org/@fluentui/merge-styles/-/merge-styles-8.5.15.tgz", "integrity": "sha512-4CdKwo4k1Un2QLulpSVIz/KMgLNBMgin4NPyapmKDMVuO1OOxJUqfocubRGNO5x9mKgAMMYwBKGO9i0uxMMpJw==", "requires": {"@fluentui/set-version": "^8.2.14", "tslib": "^2.1.0"}}, "@fluentui/priority-overflow": {"version": "9.1.11", "resolved": "https://registry.npmjs.org/@fluentui/priority-overflow/-/priority-overflow-9.1.11.tgz", "integrity": "sha512-sdrpavvKX2kepQ1d6IaI3ObLq5SAQBPRHPGx2+wiMWL7cEx9vGGM0fmeicl3soqqmM5uwCmWnZk9QZv9XOY98w==", "requires": {"@swc/helpers": "^0.5.1"}}, "@fluentui/react": {"version": "8.114.4", "resolved": "https://registry.npmjs.org/@fluentui/react/-/react-8.114.4.tgz", "integrity": "sha512-dVpfFSpWUxdyqWlCVSXX5d34S760h4MaQjGR2/TPavtcJRRpJDHbBN2Hn7s4riA6YX5N7bTdN372UvIVbBbzuw==", "requires": {"@fluentui/date-time-utilities": "^8.5.16", "@fluentui/font-icons-mdl2": "^8.5.31", "@fluentui/foundation-legacy": "^8.2.51", "@fluentui/merge-styles": "^8.5.15", "@fluentui/react-focus": "^8.8.38", "@fluentui/react-hooks": "^8.6.36", "@fluentui/react-portal-compat-context": "^9.0.11", "@fluentui/react-window-provider": "^2.2.18", "@fluentui/set-version": "^8.2.14", "@fluentui/style-utilities": "^8.10.2", "@fluentui/theme": "^2.6.41", "@fluentui/utilities": "^8.13.24", "@microsoft/load-themed-styles": "^1.10.26", "tslib": "^2.1.0"}}, "@fluentui/react-accordion": {"version": "9.3.38", "resolved": "https://registry.npmjs.org/@fluentui/react-accordion/-/react-accordion-9.3.38.tgz", "integrity": "sha512-BB8d9+Jr0v4SW58OJTIyvsxhA/iOBbvIkQZlVHKqt4tL8dHOIFPrApw5WqQqaSYJsEwt4HxmlNU4Dv8qRughbg==", "requires": {"@fluentui/react-aria": "^9.7.3", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-alert": {"version": "9.0.0-beta.104", "resolved": "https://registry.npmjs.org/@fluentui/react-alert/-/react-alert-9.0.0-beta.104.tgz", "integrity": "sha512-Z8BGSyzEKok5wlJF2cUc8GUj2q+c1D+119YF0WtHLiieh7pwOHjBcDJOHqnaVnQNbhetIA3NUht2z0e1wgOK5w==", "requires": {"@fluentui/react-avatar": "^9.6.9", "@fluentui/react-button": "^9.3.65", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-aria": {"version": "9.7.3", "resolved": "https://registry.npmjs.org/@fluentui/react-aria/-/react-aria-9.7.3.tgz", "integrity": "sha512-YwyPNEcBDCdY6YzhrIrtlSrLs2Le7X1jLq9em8OnqHeiO22dBmg5xlBJoAMwJ8awCpI9xhu1PhU/2VJY4YqNuA==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-utilities": "^9.16.1", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-avatar": {"version": "9.6.9", "resolved": "https://registry.npmjs.org/@fluentui/react-avatar/-/react-avatar-9.6.9.tgz", "integrity": "sha512-3aZeUhGOg+UlHsp2x//G4VKRWKclcsZvX6L9UVnHsA/nQqRw7C5Bfo9iFNsEeJ3R5W5mFA6LyEFWedJ7QdAmdQ==", "requires": {"@fluentui/react-badge": "^9.2.22", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-popover": "^9.8.33", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-tooltip": "^9.4.11", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-badge": {"version": "9.2.22", "resolved": "https://registry.npmjs.org/@fluentui/react-badge/-/react-badge-9.2.22.tgz", "integrity": "sha512-zzimP5mZiiCOm8expUTzD6yvvKbnKq22PK/L6+oNpifrvQnDwJF/0nwXQVjA3+icNoYTaHe/q0fFivpXV+Js6g==", "requires": {"@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-breadcrumb": {"version": "9.0.11", "resolved": "https://registry.npmjs.org/@fluentui/react-breadcrumb/-/react-breadcrumb-9.0.11.tgz", "integrity": "sha512-L+AQqZz1gqkScD8IW1CjZWGNrDaHDc/gSv+PrvgSZeGDPibGj6TnLygJ7BKM+rQ+Hc2SbCogKbERpQZCbrSFvA==", "requires": {"@fluentui/react-aria": "^9.7.3", "@fluentui/react-button": "^9.3.65", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-link": "^9.2.7", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-button": {"version": "9.3.65", "resolved": "https://registry.npmjs.org/@fluentui/react-button/-/react-button-9.3.65.tgz", "integrity": "sha512-3VOt29AugkfR7VMnkKON449E7Sn/nvc6BBT4kJDGKQY+Nm5d2p9e4HmHp1UaM9zRPt47lagTY2WFJNrKKSe/BA==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-card": {"version": "9.0.64", "resolved": "https://registry.npmjs.org/@fluentui/react-card/-/react-card-9.0.64.tgz", "integrity": "sha512-TB/Zk+tLDUPNyAd2y8BvN0T2nroimtBOpB5GTK72E5sWPk0kaKIHwBEfXxNFGdGXcw0TAmVNqYi4ks37vh0Rgg==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-checkbox": {"version": "9.2.8", "resolved": "https://registry.npmjs.org/@fluentui/react-checkbox/-/react-checkbox-9.2.8.tgz", "integrity": "sha512-L4aWzeZdi98d0ZhgNPtxghfhasQv1qlxIRMaPxtwvk5TN6i9YmRF8vf5Pmf0PESjT+zp3VPcisHcIfcqG26SmQ==", "requires": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-combobox": {"version": "9.7.0", "resolved": "https://registry.npmjs.org/@fluentui/react-combobox/-/react-combobox-9.7.0.tgz", "integrity": "sha512-YmTdg04rvsg2+Dkw3ob+YLnS9rm3TLVMMNYTH0T64/FM3qirHntIXGbhMZXP5Cdo14gzQwr/e78NjBRKfYO4Wg==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-field": "^9.1.50", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-positioning": "^9.12.4", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-components": {"version": "9.45.0", "resolved": "https://registry.npmjs.org/@fluentui/react-components/-/react-components-9.45.0.tgz", "integrity": "sha512-Y+Laj1dvRcCp/nWT0DExRXoh7oKTX458g6oltrGjhIHikq4D6/kssK5tfhCyknPLwIlVSYi5J+G6L3NfvI8a8w==", "requires": {"@fluentui/react-accordion": "^9.3.38", "@fluentui/react-alert": "9.0.0-beta.104", "@fluentui/react-avatar": "^9.6.9", "@fluentui/react-badge": "^9.2.22", "@fluentui/react-breadcrumb": "^9.0.11", "@fluentui/react-button": "^9.3.65", "@fluentui/react-card": "^9.0.64", "@fluentui/react-checkbox": "^9.2.8", "@fluentui/react-combobox": "^9.7.0", "@fluentui/react-dialog": "^9.9.7", "@fluentui/react-divider": "^9.2.58", "@fluentui/react-drawer": "^9.1.1", "@fluentui/react-field": "^9.1.50", "@fluentui/react-image": "^9.1.55", "@fluentui/react-infobutton": "9.0.0-beta.88", "@fluentui/react-infolabel": "^9.0.16", "@fluentui/react-input": "^9.4.60", "@fluentui/react-label": "^9.1.58", "@fluentui/react-link": "^9.2.7", "@fluentui/react-menu": "^9.12.45", "@fluentui/react-message-bar": "^9.0.16", "@fluentui/react-overflow": "^9.1.8", "@fluentui/react-persona": "^9.2.68", "@fluentui/react-popover": "^9.8.33", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-positioning": "^9.12.4", "@fluentui/react-progress": "^9.1.60", "@fluentui/react-provider": "^9.13.8", "@fluentui/react-radio": "^9.2.3", "@fluentui/react-select": "^9.1.60", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-skeleton": "^9.0.48", "@fluentui/react-slider": "^9.1.65", "@fluentui/react-spinbutton": "^9.2.60", "@fluentui/react-spinner": "^9.3.38", "@fluentui/react-switch": "^9.1.65", "@fluentui/react-table": "^9.11.5", "@fluentui/react-tabs": "^9.4.6", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-tags": "^9.0.22", "@fluentui/react-text": "^9.4.7", "@fluentui/react-textarea": "^9.3.60", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-toast": "^9.3.27", "@fluentui/react-toolbar": "^9.1.66", "@fluentui/react-tooltip": "^9.4.11", "@fluentui/react-tree": "^9.4.25", "@fluentui/react-utilities": "^9.16.1", "@fluentui/react-virtualizer": "9.0.0-alpha.66", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-context-selector": {"version": "9.1.49", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.1.49.tgz", "integrity": "sha512-u4wRNfnyfuZDalVEESBPFQ0Ue4yYu+ozkPQvuEV6kriQGnAQQyyVbIidOCuP7Sja0nBwgM8eAzK0uX/slmmj3Q==", "requires": {"@fluentui/react-utilities": "^9.16.1", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-dialog": {"version": "9.9.7", "resolved": "https://registry.npmjs.org/@fluentui/react-dialog/-/react-dialog-9.9.7.tgz", "integrity": "sha512-5/6MeaHOYpx8Vt0auMJGLCjn6O1IYtl6IhwdwRNXL6AS1o4F24IKXdWZPtiHWuvzbuZAQd3+5nRDUE5KC9We6A==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1", "react-transition-group": "^4.4.1"}}, "@fluentui/react-divider": {"version": "9.2.58", "resolved": "https://registry.npmjs.org/@fluentui/react-divider/-/react-divider-9.2.58.tgz", "integrity": "sha512-y1ECy1zM4imKhpyOyUGugB+J30tfySO5hhrsIcpaiUQxRjE4IhZf2ZG6EqAQYLinJ+hV06yLZoazekljlvk6yw==", "requires": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-drawer": {"version": "9.1.1", "resolved": "https://registry.npmjs.org/@fluentui/react-drawer/-/react-drawer-9.1.1.tgz", "integrity": "sha512-3zvbbeaLLJZa4MXRpW8Ta4DFZ5457Tq9/4a0CqsIW/+8EuwtJwO+FB5a0DS6j0q6kN4mjkWF19OvzMkJsSTRVw==", "requires": {"@fluentui/react-dialog": "^9.9.7", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-motion-preview": "^0.5.10", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-field": {"version": "9.1.50", "resolved": "https://registry.npmjs.org/@fluentui/react-field/-/react-field-9.1.50.tgz", "integrity": "sha512-2mbx7YReMWvrgi3set9KepLLgMyNJ7StLu/HiHMM3jkcgPt3mGfwoJEsEKt+xd8eUAo4b82F7t+tHI4f9yzJaQ==", "requires": {"@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-focus": {"version": "8.8.38", "resolved": "https://registry.npmjs.org/@fluentui/react-focus/-/react-focus-8.8.38.tgz", "integrity": "sha512-vnsaY7hJSPIJBxm5Pj0FrcFDumV6kKgFVpsKsEKJzb1D88rDDLcmvz9jWUx68a3ru6idEbZYmyePGT1IiRsAug==", "requires": {"@fluentui/keyboard-key": "^0.4.14", "@fluentui/merge-styles": "^8.5.15", "@fluentui/set-version": "^8.2.14", "@fluentui/style-utilities": "^8.10.2", "@fluentui/utilities": "^8.13.24", "tslib": "^2.1.0"}}, "@fluentui/react-hooks": {"version": "8.6.36", "resolved": "https://registry.npmjs.org/@fluentui/react-hooks/-/react-hooks-8.6.36.tgz", "integrity": "sha512-kI0Z4Q4xHUs4SOmmI5n5OH5fPckqMSCovTRpiuxzCO2TNzLmfC861+nqf4Ygw/ChqNm2gWNZZfUADfnNAEsq+Q==", "requires": {"@fluentui/react-window-provider": "^2.2.18", "@fluentui/set-version": "^8.2.14", "@fluentui/utilities": "^8.13.24", "tslib": "^2.1.0"}}, "@fluentui/react-icons": {"version": "2.0.225", "resolved": "https://registry.npmjs.org/@fluentui/react-icons/-/react-icons-2.0.225.tgz", "integrity": "sha512-L9phN3bAMlZCa5+/ObGjIO+5GI8M50ym766sraSq92jaJwgAXrCJDLWuDGWZRGrC63DcagtR2culptj3q7gMMg==", "requires": {"@griffel/react": "^1.0.0", "tslib": "^2.1.0"}}, "@fluentui/react-image": {"version": "9.1.55", "resolved": "https://registry.npmjs.org/@fluentui/react-image/-/react-image-9.1.55.tgz", "integrity": "sha512-hYP61OWLuGSJNPOGJXtphbiDESfLB+/vsODKQsJhrDRJ2CSNMAfNznPHucqGRRN6AWQOI/BynJDS5F22Y//7CQ==", "requires": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-infobutton": {"version": "9.0.0-beta.88", "resolved": "https://registry.npmjs.org/@fluentui/react-infobutton/-/react-infobutton-9.0.0-beta.88.tgz", "integrity": "sha512-NVZyfrLtoFNu7cGkp2ORWsxJiCk1JgN4CVBDj03QSIh14EsPMwphYgDwfQ8TZOF2Nub0DGtC7/tF8IUlb/aP6g==", "requires": {"@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-popover": "^9.8.33", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-infolabel": {"version": "9.0.16", "resolved": "https://registry.npmjs.org/@fluentui/react-infolabel/-/react-infolabel-9.0.16.tgz", "integrity": "sha512-UCY+2vB4vOn0LfVhbgkyNG0EiuKIe0PdxEAtLU2PqosHLkaLKnYDKJdiIS/oaFmyNtGHmMxRkigvZpZ7h74f9g==", "requires": {"@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-popover": "^9.8.33", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-input": {"version": "9.4.60", "resolved": "https://registry.npmjs.org/@fluentui/react-input/-/react-input-9.4.60.tgz", "integrity": "sha512-kuk24K0X0gckTCssXoiWvZsTFVpZJv+WPl2fkjxeffzmFfBZtJUFQkXeC4/hcAg+aScjZnEtqjHjwDEbjZqkeA==", "requires": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-jsx-runtime": {"version": "9.0.27", "resolved": "https://registry.npmjs.org/@fluentui/react-jsx-runtime/-/react-jsx-runtime-9.0.27.tgz", "integrity": "sha512-9wxsWxVI7RLXsdK+7lzp7TK0FJKnrrj+Igxn0prqAvXdBRiFcuycoCJaHzC4Ka+Hsiol8NQg6xaIR59a28lmyQ==", "requires": {"@fluentui/react-utilities": "^9.16.1", "@swc/helpers": "^0.5.1", "react-is": "^17.0.2"}}, "@fluentui/react-label": {"version": "9.1.58", "resolved": "https://registry.npmjs.org/@fluentui/react-label/-/react-label-9.1.58.tgz", "integrity": "sha512-0ouSMop4vpXJzMvAyfmIr3TgDM/W1k+GFm8ZPD5fDQCopSJ+h3kvUZg5pqaXpBwamvZ16+qRARfTNITp2U7Rjw==", "requires": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-link": {"version": "9.2.7", "resolved": "https://registry.npmjs.org/@fluentui/react-link/-/react-link-9.2.7.tgz", "integrity": "sha512-z4X9dcUc/7FlqDxbGKbOfWubru+QimtzgMtlVxZ30pkC959hfIbFpbBY6Me76UOuFiOZxUPdfyY/73ekhhhVxw==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-menu": {"version": "9.12.45", "resolved": "https://registry.npmjs.org/@fluentui/react-menu/-/react-menu-9.12.45.tgz", "integrity": "sha512-qhpmuvAB4DUmmC5lNMakVvZjTdj/GZnH6WctNGZp94iCZLhcnIQcM9l0PvRpUpU1v3irXRyE5QV+x+wXC0awTw==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-positioning": "^9.12.4", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-message-bar": {"version": "9.0.16", "resolved": "https://registry.npmjs.org/@fluentui/react-message-bar/-/react-message-bar-9.0.16.tgz", "integrity": "sha512-R1VnqcFwu0pM2Yk8rjkN48Lx/n44UFD13BuY8/JeEuU8XQ8hLnEBVtdHjzRPJk+iM5in2ScMMQj4Z0nWyCRM1Q==", "requires": {"@fluentui/react-button": "^9.3.65", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1", "react-transition-group": "^4.4.1"}}, "@fluentui/react-motion-preview": {"version": "0.5.10", "resolved": "https://registry.npmjs.org/@fluentui/react-motion-preview/-/react-motion-preview-0.5.10.tgz", "integrity": "sha512-6iwF3N4hB6IxCoFVusgA2mp6mrTknwcsVGNYEQw1YF5WgGOMF3M0N1xNpN61/SYziT6HSUaI38NaA7LI3Dp3Sw==", "requires": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-overflow": {"version": "9.1.8", "resolved": "https://registry.npmjs.org/@fluentui/react-overflow/-/react-overflow-9.1.8.tgz", "integrity": "sha512-W8L68+0bUtfGr72LRx+U05EZLO0E8VMfscDiNKiEjDrOqdBnqNAIDN86825wrN77HH2wvILN07EhPOauqzz8YQ==", "requires": {"@fluentui/priority-overflow": "^9.1.11", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-persona": {"version": "9.2.68", "resolved": "https://registry.npmjs.org/@fluentui/react-persona/-/react-persona-9.2.68.tgz", "integrity": "sha512-C<PERSON>tDiZ34GGaw7lZ85uHZOuYXzkY21VHN6cUlGY1TJn98+Xz+y7JoVLIG7KZHHp2JzmmjtwjvgnqAdOun5LrWig==", "requires": {"@fluentui/react-avatar": "^9.6.9", "@fluentui/react-badge": "^9.2.22", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-popover": {"version": "9.8.33", "resolved": "https://registry.npmjs.org/@fluentui/react-popover/-/react-popover-9.8.33.tgz", "integrity": "sha512-0yPX6KCdMEGmrvJnQles5iTKN0OZ2vNSPVdkbyEKIUKj5DrNK1cMZEV/7Tgrtn922fx3/74FLMqEpEDTdrvQ/Q==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-positioning": "^9.12.4", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-portal": {"version": "9.4.10", "resolved": "https://registry.npmjs.org/@fluentui/react-portal/-/react-portal-9.4.10.tgz", "integrity": "sha512-k8fTRv9wTPSPCuNBFE2HxIhXsVYoG6Azb6Ib2xaDK+nczoW2WbsmNmwBJGEGi8UKjIoQzV+95KsYQ9me+uqKPA==", "requires": {"@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1", "use-disposable": "^1.0.1"}}, "@fluentui/react-portal-compat-context": {"version": "9.0.11", "resolved": "https://registry.npmjs.org/@fluentui/react-portal-compat-context/-/react-portal-compat-context-9.0.11.tgz", "integrity": "sha512-ubvW/ej0O+Pago9GH3mPaxzUgsNnBoqvghNamWjyKvZIViyaXUG6+sgcAl721R+qGAFac+A20akI5qDJz/xtdg==", "requires": {"@swc/helpers": "^0.5.1"}}, "@fluentui/react-positioning": {"version": "9.12.4", "resolved": "https://registry.npmjs.org/@fluentui/react-positioning/-/react-positioning-9.12.4.tgz", "integrity": "sha512-qQAjHF/FJFs2TyK0x08t0iFtDQlGNGH0OFC3jrG1xIFEe3nFPoeYeNT3zxOmj+D7bvlcJTIITcoe++YQTnCf4w==", "requires": {"@floating-ui/devtools": "0.2.1", "@floating-ui/dom": "^1.2.0", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-progress": {"version": "9.1.60", "resolved": "https://registry.npmjs.org/@fluentui/react-progress/-/react-progress-9.1.60.tgz", "integrity": "sha512-9wC7lWdo3S8rhxKWlIhcYAzsZNw+rL2HvNJTvEvFxXcOG7nJxP/3mGclV/jCCwDoPDnt9BT+40pGK84eD0BNIA==", "requires": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-provider": {"version": "9.13.8", "resolved": "https://registry.npmjs.org/@fluentui/react-provider/-/react-provider-9.13.8.tgz", "integrity": "sha512-FCvDMjs/BNAcqJuHU+kN/lqLB2RDQ/LQo29ltfLKFlTR1nTUNJvPMOVhjj6eEt+t81628LOYhbbaXOj9rYtfGg==", "requires": {"@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/core": "^1.14.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-radio": {"version": "9.2.3", "resolved": "https://registry.npmjs.org/@fluentui/react-radio/-/react-radio-9.2.3.tgz", "integrity": "sha512-8eKeUL0ZNr792Q6NGWPp7dpOV2IFcjAQ2oWR2/bruQVu8LMzYYKe2o6pQWdCag6UGPZuszkms9Xl7zPdDQBUdA==", "requires": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-select": {"version": "9.1.60", "resolved": "https://registry.npmjs.org/@fluentui/react-select/-/react-select-9.1.60.tgz", "integrity": "sha512-4HfRRTlGStOgtO00RY6jmOwz6MXnoa9gtjkV7StLmJZ2U5NTjVUrnp2dP1Vjb6hO13xaihWGEYyYKnsQ3R7kIw==", "requires": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-shared-contexts": {"version": "9.14.0", "resolved": "https://registry.npmjs.org/@fluentui/react-shared-contexts/-/react-shared-contexts-9.14.0.tgz", "integrity": "sha512-P9yhg31WYfB1W66/gD3+qVCLBsyIEcOzQvKVaIQvd9UhF67lNW4kMXUB6YVOk5PV0Og4hXnkH/vuHl7YMD9RHw==", "requires": {"@fluentui/react-theme": "^9.1.16", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-skeleton": {"version": "9.0.48", "resolved": "https://registry.npmjs.org/@fluentui/react-skeleton/-/react-skeleton-9.0.48.tgz", "integrity": "sha512-P0Rw5hIOn5CrZIWg7nVoK3gamxFhZI80KcRVaWap4O3gLo5C8nKHJWOtyBQZ5WKH+S6hoEGZ2USL6CoyXslxeQ==", "requires": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-slider": {"version": "9.1.65", "resolved": "https://registry.npmjs.org/@fluentui/react-slider/-/react-slider-9.1.65.tgz", "integrity": "sha512-7kuJMIojxCmNOuiRmQwh9iiXx8zwxkrgvsWmReRIBX0WB6w1VqMcuuikq2Z2ISgNPmepCX8W+qDfx8Ne4F/HtQ==", "requires": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-spinbutton": {"version": "9.2.60", "resolved": "https://registry.npmjs.org/@fluentui/react-spinbutton/-/react-spinbutton-9.2.60.tgz", "integrity": "sha512-0IIxEH0CTf4fNMoyvMa37bc63+0ZlznlsNy8lF3hujAT8Z9sUKVMH68e6tGUuXGJIkCUyDKU8HA+9FF2DyPvNA==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-field": "^9.1.50", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-spinner": {"version": "9.3.38", "resolved": "https://registry.npmjs.org/@fluentui/react-spinner/-/react-spinner-9.3.38.tgz", "integrity": "sha512-dPJr7/rgU2Qe/K2BciJTAEwEd0ytGpCw3VOVyK2T25w7Jw5RAHmgP+mbw+7se44Mr6sd1LH76mh5sfmQ3tODgw==", "requires": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-switch": {"version": "9.1.65", "resolved": "https://registry.npmjs.org/@fluentui/react-switch/-/react-switch-9.1.65.tgz", "integrity": "sha512-P0DwogD6hZJ3O005zCFPDoFXuzkrpKMrAeQGh9X0fqFP5JyHXVCgAAZQOLcphbbT9QukoEF5irN2Z4L9gBn57A==", "requires": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-label": "^9.1.58", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-table": {"version": "9.11.5", "resolved": "https://registry.npmjs.org/@fluentui/react-table/-/react-table-9.11.5.tgz", "integrity": "sha512-roQ<PERSON><PERSON>tl1aqXlachS2oTraVE45x3KdDrX0KyQGCdcQRxNprXJW6dIK9QjlbAL6yAsAMDafmFA4y9uRxl408dQ==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-avatar": "^9.6.9", "@fluentui/react-checkbox": "^9.2.8", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-radio": "^9.2.3", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-tabs": {"version": "9.4.6", "resolved": "https://registry.npmjs.org/@fluentui/react-tabs/-/react-tabs-9.4.6.tgz", "integrity": "sha512-LQvibLeJFyqKKiOjZUkRvbfLtsVosUhNUdh1SCQUPxQVpEPSK6XgwK0A1+jjoVhKn+PAJakxRINgnvqQD8pQBA==", "requires": {"@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-tabster": {"version": "9.17.3", "resolved": "https://registry.npmjs.org/@fluentui/react-tabster/-/react-tabster-9.17.3.tgz", "integrity": "sha512-cFcUYrkGW15w5yXzCPTTVG/7x5kNXxnhQXuh8SPyCc9JZeG7XI3+hy1T37PsXGxNS4KN9ePHkBHzgDfYO4gzYQ==", "requires": {"@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1", "keyborg": "^2.3.0", "tabster": "^5.0.1"}}, "@fluentui/react-tags": {"version": "9.0.22", "resolved": "https://registry.npmjs.org/@fluentui/react-tags/-/react-tags-9.0.22.tgz", "integrity": "sha512-gQIOCVu3HIfGjtAmwOnwBEnTsNyRBU8Pvs6EugpUyyqkRjzbm5TnL3LtiUy4f6/+NuaRqcYAvhwpdUhrlciwcA==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-avatar": "^9.6.9", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-text": {"version": "9.4.7", "resolved": "https://registry.npmjs.org/@fluentui/react-text/-/react-text-9.4.7.tgz", "integrity": "sha512-c6uJ98B35L8sviYxhQj1i+LW+HVNDdco2ImS9VLv/Duo4HiYs1G2y1YhtBDDiGxLe2moIvfg9ajDzMZV29aXFw==", "requires": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-textarea": {"version": "9.3.60", "resolved": "https://registry.npmjs.org/@fluentui/react-textarea/-/react-textarea-9.3.60.tgz", "integrity": "sha512-wH4MBWT4EOgNH9FXTjcgH34oANUaoduhmVjffnxaPl3R767Ak0fZPG7kky7yrLMjTDUSwILsEj/q+hsN6o+7Ag==", "requires": {"@fluentui/react-field": "^9.1.50", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-theme": {"version": "9.1.16", "resolved": "https://registry.npmjs.org/@fluentui/react-theme/-/react-theme-9.1.16.tgz", "integrity": "sha512-QK2dGE5aQXN1UGdiEmGKpYGP3tHXIchLvFf8DEEOWnF4XBc9SiEPNFYkvLMJjHxZmDz4D670rsOPe0r5jFDEKQ==", "requires": {"@fluentui/tokens": "1.0.0-alpha.13", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-toast": {"version": "9.3.27", "resolved": "https://registry.npmjs.org/@fluentui/react-toast/-/react-toast-9.3.27.tgz", "integrity": "sha512-DbRAYyL5Bd/pcFiGHPpK+rQMyc4LBll9YBy496l97dGDO2HmqFuiwP74V1KznxLcr4inCNWwThIJws5VLFsJLg==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1", "react-transition-group": "^4.4.1"}}, "@fluentui/react-toolbar": {"version": "9.1.66", "resolved": "https://registry.npmjs.org/@fluentui/react-toolbar/-/react-toolbar-9.1.66.tgz", "integrity": "sha512-ooNTp1R5MBZwiVK8fiJu29gE48vUx4NbXdwB2yHcCprasG3asjuoKQfOYM4+1NfFA0DetVrbK8L46IBeZyeBvA==", "requires": {"@fluentui/react-button": "^9.3.65", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-divider": "^9.2.58", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-radio": "^9.2.3", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-tooltip": {"version": "9.4.11", "resolved": "https://registry.npmjs.org/@fluentui/react-tooltip/-/react-tooltip-9.4.11.tgz", "integrity": "sha512-HXm8yYuAHJuczeFExco0WQSjO3DzDj5AJxqICHF8qtbtihUKfWpPnKM1qQWR+yJR2zc2jzvOEIzZXEkxSG+fSg==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-portal": "^9.4.10", "@fluentui/react-positioning": "^9.12.4", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-tree": {"version": "9.4.25", "resolved": "https://registry.npmjs.org/@fluentui/react-tree/-/react-tree-9.4.25.tgz", "integrity": "sha512-7IMqnOiNFMRuPujnbxJUYD8AEh0z1OGXkdNkAeLyj3pkwuvQs9+TbaNtv5Z372YN+kwYF4EYalYcPuNsRlx7cQ==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-aria": "^9.7.3", "@fluentui/react-avatar": "^9.6.9", "@fluentui/react-button": "^9.3.65", "@fluentui/react-checkbox": "^9.2.8", "@fluentui/react-context-selector": "^9.1.49", "@fluentui/react-icons": "^2.0.224", "@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-radio": "^9.2.3", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-tabster": "^9.17.3", "@fluentui/react-theme": "^9.1.16", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-utilities": {"version": "9.16.1", "resolved": "https://registry.npmjs.org/@fluentui/react-utilities/-/react-utilities-9.16.1.tgz", "integrity": "sha512-2wdwmgTFcVy14ZLbRNJ8Q6dCCBLekkJ8Znnok68gKRLDcwpPT3UjSraoU+DGjOA5BMfPppZBU8Yb5GqdIfd48g==", "requires": {"@fluentui/keyboard-keys": "^9.0.7", "@fluentui/react-shared-contexts": "^9.14.0", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-virtualizer": {"version": "9.0.0-alpha.66", "resolved": "https://registry.npmjs.org/@fluentui/react-virtualizer/-/react-virtualizer-9.0.0-alpha.66.tgz", "integrity": "sha512-x/ZOAIAwctt7pvOBIzS4iZGU0ahiPhQFS7iAHksFkF9LimneaV92A/02dW0Cy4v7dv9wZNoosQwhS05Yx3DVDQ==", "requires": {"@fluentui/react-jsx-runtime": "^9.0.27", "@fluentui/react-shared-contexts": "^9.14.0", "@fluentui/react-utilities": "^9.16.1", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-window-provider": {"version": "2.2.18", "resolved": "https://registry.npmjs.org/@fluentui/react-window-provider/-/react-window-provider-2.2.18.tgz", "integrity": "sha512-nBKqxd0P8NmIR0qzFvka1urE2LVbUm6cse1I1T7TcOVNYa5jDf5BrO06+JRZfwbn00IJqOnIVoP0qONqceypWQ==", "requires": {"@fluentui/set-version": "^8.2.14", "tslib": "^2.1.0"}}, "@fluentui/set-version": {"version": "8.2.14", "resolved": "https://registry.npmjs.org/@fluentui/set-version/-/set-version-8.2.14.tgz", "integrity": "sha512-f/QWJnSeyfAjGAqq57yjMb6a5ejPlwfzdExPmzFBuEOuupi8hHbV8Yno12XJcTW4I0KXEQGw+PUaM1aOf/j7jw==", "requires": {"tslib": "^2.1.0"}}, "@fluentui/style-utilities": {"version": "8.10.2", "resolved": "https://registry.npmjs.org/@fluentui/style-utilities/-/style-utilities-8.10.2.tgz", "integrity": "sha512-ocELtMb/85nBa3rSfiAIwYx6TydN+3rQqv1P0H/L7etYNNtxOfS86JSWfn8zAsHMejbwUKJ1ZsIKs47c598XGQ==", "requires": {"@fluentui/merge-styles": "^8.5.15", "@fluentui/set-version": "^8.2.14", "@fluentui/theme": "^2.6.41", "@fluentui/utilities": "^8.13.24", "@microsoft/load-themed-styles": "^1.10.26", "tslib": "^2.1.0"}}, "@fluentui/theme": {"version": "2.6.41", "resolved": "https://registry.npmjs.org/@fluentui/theme/-/theme-2.6.41.tgz", "integrity": "sha512-h9RguEzqzJ0+59ys5Kkp7JtsjhDUxBLmQunu5rpHp5Mp788OtEjI/n1a9FIcOAL/priPSQwXN7RbuDpeP7+aSw==", "requires": {"@fluentui/merge-styles": "^8.5.15", "@fluentui/set-version": "^8.2.14", "@fluentui/utilities": "^8.13.24", "tslib": "^2.1.0"}}, "@fluentui/tokens": {"version": "1.0.0-alpha.13", "resolved": "https://registry.npmjs.org/@fluentui/tokens/-/tokens-1.0.0-alpha.13.tgz", "integrity": "sha512-IzYysTTBkAH7tQZxYKpzhxYnTJkvwXhjhTOpmERgnqTFifHTP8/vaQjJAAm7dI/9zlDx1oN+y/I+KzL9bDLHZQ==", "requires": {"@swc/helpers": "^0.5.1"}}, "@fluentui/utilities": {"version": "8.13.24", "resolved": "https://registry.npmjs.org/@fluentui/utilities/-/utilities-8.13.24.tgz", "integrity": "sha512-/jo6hWCzTGCx06l2baAMwsjjBZ/dyMouls53uNaQLUGUUhUwXh/DcDDXMqLRJB3MaH9zvgfvRw61iKmm2s9fIA==", "requires": {"@fluentui/dom-utilities": "^2.2.14", "@fluentui/merge-styles": "^8.5.15", "@fluentui/set-version": "^8.2.14", "tslib": "^2.1.0"}}, "@griffel/core": {"version": "1.15.2", "resolved": "https://registry.npmjs.org/@griffel/core/-/core-1.15.2.tgz", "integrity": "sha512-RlsIXoSS3gaYykUgxFpwKAs/DV9cRUKp3CW1kt3iPAtsDTWn/o+8bT1jvBws/tMM2GBu/Uc0EkaIzUPqD7uA+Q==", "requires": {"@emotion/hash": "^0.9.0", "@griffel/style-types": "^1.0.3", "csstype": "^3.1.3", "rtl-css-js": "^1.16.1", "stylis": "^4.2.0", "tslib": "^2.1.0"}}, "@griffel/react": {"version": "1.5.20", "resolved": "https://registry.npmjs.org/@griffel/react/-/react-1.5.20.tgz", "integrity": "sha512-1P2yaPctENFSCwyPIYXBmgpNH68c0lc/jwSzPij1QATHDK1AASKuSeq6hW108I67RKjhRyHCcALshdZ3GcQXSg==", "requires": {"@griffel/core": "^1.15.2", "tslib": "^2.1.0"}}, "@griffel/style-types": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/@griffel/style-types/-/style-types-1.0.3.tgz", "integrity": "sha512-AzbbYV/EobNIBtfMtyu2edFin895gjVxtu1nsRhTETUAIb0/LCZoue3Jd/kFLuPwe95rv5WRUBiQpVwJsrrFcw==", "requires": {"csstype": "^3.1.3"}}, "@jridgewell/gen-mapping": {"version": "0.3.8", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz", "integrity": "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==", "dev": true, "requires": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}}, "@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==", "dev": true}, "@jridgewell/set-array": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz", "integrity": "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==", "dev": true}, "@jridgewell/sourcemap-codec": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "integrity": "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==", "dev": true}, "@jridgewell/trace-mapping": {"version": "0.3.25", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "integrity": "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==", "dev": true, "requires": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "@microsoft/load-themed-styles": {"version": "1.10.295", "resolved": "https://registry.npmjs.org/@microsoft/load-themed-styles/-/load-themed-styles-1.10.295.tgz", "integrity": "sha512-W+IzEBw8a6LOOfRJM02dTT7BDZijxm+Z7lhtOAz1+y9vQm1Kdz9jlAO+qCEKsfxtUOmKilW8DIRqFw2aUgKeGg=="}, "@react-spring/animated": {"version": "9.7.3", "resolved": "https://registry.npmjs.org/@react-spring/animated/-/animated-9.7.3.tgz", "integrity": "sha512-5CWeNJt9pNgyvuSzQH+uy2pvTg8Y4/OisoscZIR8/ZNLIOI+CatFBhGZpDGTF/OzdNFsAoGk3wiUYTwoJ0YIvw==", "requires": {"@react-spring/shared": "~9.7.3", "@react-spring/types": "~9.7.3"}}, "@react-spring/core": {"version": "9.7.3", "resolved": "https://registry.npmjs.org/@react-spring/core/-/core-9.7.3.tgz", "integrity": "sha512-IqFdPVf3ZOC1Cx7+M0cXf4odNLxDC+n7IN3MDcVCTIOSBfqEcBebSv+vlY5AhM0zw05PDbjKrNmBpzv/AqpjnQ==", "requires": {"@react-spring/animated": "~9.7.3", "@react-spring/shared": "~9.7.3", "@react-spring/types": "~9.7.3"}}, "@react-spring/shared": {"version": "9.7.3", "resolved": "https://registry.npmjs.org/@react-spring/shared/-/shared-9.7.3.tgz", "integrity": "sha512-NEopD+9S5xYyQ0pGtioacLhL2luflh6HACSSDUZOwLHoxA5eku1UPuqcJqjwSD6luKjjLfiLOspxo43FUHKKSA==", "requires": {"@react-spring/types": "~9.7.3"}}, "@react-spring/types": {"version": "9.7.3", "resolved": "https://registry.npmjs.org/@react-spring/types/-/types-9.7.3.tgz", "integrity": "sha512-Kpx/fQ/ZFX31OtlqVEFfgaD1ACzul4NksrvIgYfIFq9JpDHFwQkMVZ10tbo0FU/grje4rcL4EIrjekl3kYwgWw=="}, "@react-spring/web": {"version": "9.7.3", "resolved": "https://registry.npmjs.org/@react-spring/web/-/web-9.7.3.tgz", "integrity": "sha512-BXt6BpS9aJL/QdVqEIX9YoUy8CE6TJrU0mNCqSoxdXlIeNcEBWOfIyE6B14ENNsyQKS3wOWkiJfco0tCr/9tUg==", "requires": {"@react-spring/animated": "~9.7.3", "@react-spring/core": "~9.7.3", "@react-spring/shared": "~9.7.3", "@react-spring/types": "~9.7.3"}}, "@remix-run/router": {"version": "1.14.2", "resolved": "https://registry.npmjs.org/@remix-run/router/-/router-1.14.2.tgz", "integrity": "sha512-ACXpdMM9hmKZww21yEqWwiLws/UPLhNKvimN8RrYSqPSvB3ov7sLvAcfvaxePeLvccTQKGdkDIhLYApZVDFuKg=="}, "@rollup/plugin-typescript": {"version": "11.1.3", "resolved": "https://registry.npmjs.org/@rollup/plugin-typescript/-/plugin-typescript-11.1.3.tgz", "integrity": "sha512-8o6cNgN44kQBcpsUJTbTXMTtb87oR1O0zgP3Dxm71hrNgparap3VujgofEilTYJo+ivf2ke6uy3/E5QEaiRlDA==", "requires": {"@rollup/pluginutils": "^5.0.1", "resolve": "^1.22.1"}}, "@rollup/pluginutils": {"version": "5.0.4", "resolved": "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-5.0.4.tgz", "integrity": "sha512-0KJnIoRI8A+a1dqOYLxH8vBf8bphDmty5QvIm2hqm7oFCFYKCAZWWd2hXgMibaPsNDhI0AtpYfQZJG47pt/k4g==", "requires": {"@types/estree": "^1.0.0", "estree-walker": "^2.0.2", "picomatch": "^2.3.1"}}, "@rollup/rollup-android-arm-eabi": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.34.9.tgz", "integrity": "sha512-qZdlImWXur0CFakn2BJ2znJOdqYZKiedEPEVNTBrpfPjc/YuTGcaYZcdmNFTkUj3DU0ZM/AElcM8Ybww3xVLzA==", "dev": true, "optional": true}, "@rollup/rollup-android-arm64": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.9.tgz", "integrity": "sha512-4KW7P53h6HtJf5Y608T1ISKvNIYLWRKMvfnG0c44M6In4DQVU58HZFEVhWINDZKp7FZps98G3gxwC1sb0wXUUg==", "dev": true, "optional": true}, "@rollup/rollup-darwin-arm64": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.34.9.tgz", "integrity": "sha512-0CY3/K54slrzLDjOA7TOjN1NuLKERBgk9nY5V34mhmuu673YNb+7ghaDUs6N0ujXR7fz5XaS5Aa6d2TNxZd0OQ==", "dev": true, "optional": true}, "@rollup/rollup-darwin-x64": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.34.9.tgz", "integrity": "sha512-eOojSEAi/acnsJVYRxnMkPFqcxSMFfrw7r2iD9Q32SGkb/Q9FpUY1UlAu1DH9T7j++gZ0lHjnm4OyH2vCI7l7Q==", "dev": true, "optional": true}, "@rollup/rollup-freebsd-arm64": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.34.9.tgz", "integrity": "sha512-2lzjQPJbN5UnHm7bHIUKFMulGTQwdvOkouJDpPysJS+QFBGDJqcfh+CxxtG23Ik/9tEvnebQiylYoazFMAgrYw==", "dev": true, "optional": true}, "@rollup/rollup-freebsd-x64": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.34.9.tgz", "integrity": "sha512-SLl0hi2Ah2H7xQYd6Qaiu01kFPzQ+hqvdYSoOtHYg/zCIFs6t8sV95kaoqjzjFwuYQLtOI0RZre/Ke0nPaQV+g==", "dev": true, "optional": true}, "@rollup/rollup-linux-arm-gnueabihf": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.34.9.tgz", "integrity": "sha512-88I+D3TeKItrw+Y/2ud4Tw0+3CxQ2kLgu3QvrogZ0OfkmX/DEppehus7L3TS2Q4lpB+hYyxhkQiYPJ6Mf5/dPg==", "dev": true, "optional": true}, "@rollup/rollup-linux-arm-musleabihf": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.34.9.tgz", "integrity": "sha512-3qyfWljSFHi9zH0KgtEPG4cBXHDFhwD8kwg6xLfHQ0IWuH9crp005GfoUUh/6w9/FWGBwEHg3lxK1iHRN1MFlA==", "dev": true, "optional": true}, "@rollup/rollup-linux-arm64-gnu": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.34.9.tgz", "integrity": "sha512-6TZjPHjKZUQKmVKMUowF3ewHxctrRR09eYyvT5eFv8w/fXarEra83A2mHTVJLA5xU91aCNOUnM+DWFMSbQ0Nxw==", "dev": true, "optional": true}, "@rollup/rollup-linux-arm64-musl": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.34.9.tgz", "integrity": "sha512-LD2fytxZJZ6xzOKnMbIpgzFOuIKlxVOpiMAXawsAZ2mHBPEYOnLRK5TTEsID6z4eM23DuO88X0Tq1mErHMVq0A==", "dev": true, "optional": true}, "@rollup/rollup-linux-loongarch64-gnu": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.34.9.tgz", "integrity": "sha512-dRAgTfDsn0TE0HI6cmo13hemKpVHOEyeciGtvlBTkpx/F65kTvShtY/EVyZEIfxFkV5JJTuQ9tP5HGBS0hfxIg==", "dev": true, "optional": true}, "@rollup/rollup-linux-powerpc64le-gnu": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.34.9.tgz", "integrity": "sha512-PHcNOAEhkoMSQtMf+rJofwisZqaU8iQ8EaSps58f5HYll9EAY5BSErCZ8qBDMVbq88h4UxaNPlbrKqfWP8RfJA==", "dev": true, "optional": true}, "@rollup/rollup-linux-riscv64-gnu": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.34.9.tgz", "integrity": "sha512-Z2i0Uy5G96KBYKjeQFKbbsB54xFOL5/y1P5wNBsbXB8yE+At3oh0DVMjQVzCJRJSfReiB2tX8T6HUFZ2k8iaKg==", "dev": true, "optional": true}, "@rollup/rollup-linux-s390x-gnu": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.34.9.tgz", "integrity": "sha512-U+5SwTMoeYXoDzJX5dhDTxRltSrIax8KWwfaaYcynuJw8mT33W7oOgz0a+AaXtGuvhzTr2tVKh5UO8GVANTxyQ==", "dev": true, "optional": true}, "@rollup/rollup-linux-x64-gnu": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.34.9.tgz", "integrity": "sha512-FwBHNSOjUTQLP4MG7y6rR6qbGw4MFeQnIBrMe161QGaQoBQLqSUEKlHIiVgF3g/mb3lxlxzJOpIBhaP+C+KP2A==", "dev": true, "optional": true}, "@rollup/rollup-linux-x64-musl": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.34.9.tgz", "integrity": "sha512-cYRpV4650z2I3/s6+5/LONkjIz8MBeqrk+vPXV10ORBnshpn8S32bPqQ2Utv39jCiDcO2eJTuSlPXpnvmaIgRA==", "dev": true, "optional": true}, "@rollup/rollup-win32-arm64-msvc": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.9.tgz", "integrity": "sha512-z4mQK9dAN6byRA/vsSgQiPeuO63wdiDxZ9yg9iyX2QTzKuQM7T4xlBoeUP/J8uiFkqxkcWndWi+W7bXdPbt27Q==", "dev": true, "optional": true}, "@rollup/rollup-win32-ia32-msvc": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.34.9.tgz", "integrity": "sha512-KB48mPtaoHy1AwDNkAJfHXvHp24H0ryZog28spEs0V48l3H1fr4i37tiyHsgKZJnCmvxsbATdZGBpbmxTE3a9w==", "dev": true, "optional": true}, "@rollup/rollup-win32-x64-msvc": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.34.9.tgz", "integrity": "sha512-AyleYRPU7+rgkMWbEh71fQlrzRfeP6SyMnRf9XX4fCdDPAJumdSBqYEcWPMzVQ4ScAl7E4oFfK0GUVn77xSwbw==", "dev": true, "optional": true}, "@swc/helpers": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.3.tgz", "integrity": "sha512-FaruWX6KdudYloq1AHD/4nU+UsMTdNE8CKyrseXWEcgjDAbvkwJg2QGPAnfIJLIWsjZOSPLOAykK6fuYp4vp4A==", "requires": {"tslib": "^2.4.0"}}, "@types/babel__core": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz", "integrity": "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==", "dev": true, "requires": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "@types/babel__generator": {"version": "7.6.8", "resolved": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.6.8.tgz", "integrity": "sha512-ASsj+tpEDsEiFr1arWrlN6V3mdfjRMZt6LtK/Vp/kreFLnr5QH5+DhvD5nINYZXzwJvXeGq+05iUXcAzVrqWtw==", "dev": true, "requires": {"@babel/types": "^7.0.0"}}, "@types/babel__template": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz", "integrity": "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==", "dev": true, "requires": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "@types/babel__traverse": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.5.tgz", "integrity": "sha512-WXCyOcRtH37HAUkpXhUduaxdm82b4GSlyTqajXviN4EfiuPgNYR109xMCKvpl6zPIpua0DGlMEDCq+g8EdoheQ==", "dev": true, "requires": {"@babel/types": "^7.20.7"}}, "@types/dompurify": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/@types/dompurify/-/dompurify-3.0.3.tgz", "integrity": "sha512-odiGr/9/qMqjcBOe5UhcNLOFHSYmKFOyr+bJ/Xu3Qp4k1pNPAlNLUVNNLcLfjQI7+W7ObX58EdD3H+3p3voOvA==", "dev": true, "requires": {"@types/trusted-types": "*"}}, "@types/estree": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/@types/estree/-/estree-1.0.6.tgz", "integrity": "sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw=="}, "@types/prop-types": {"version": "15.7.5", "resolved": "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.5.tgz", "integrity": "sha512-JCB8C6SnDoQf0cNycqd/35A7MjcnK+ZTqE7judS6o7utxUCg6imJg3QK2qzHKszlTjcj2cn+NwMB2i96ubpj7w=="}, "@types/react": {"version": "18.2.48", "resolved": "https://registry.npmjs.org/@types/react/-/react-18.2.48.tgz", "integrity": "sha512-qboRCl6Ie70DQQG9hhNREz81jqC1cs9EVNcjQ1AU+jH6NFfSAhVVbrrY/+nSF+Bsk4AOwm9Qa61InvMCyV+H3w==", "requires": {"@types/prop-types": "*", "@types/scheduler": "*", "csstype": "^3.0.2"}}, "@types/react-dom": {"version": "18.2.18", "resolved": "https://registry.npmjs.org/@types/react-dom/-/react-dom-18.2.18.tgz", "integrity": "sha512-TJxDm6OfAX2KJWJdMEVTwWke5Sc/E/RlnPGvGfS0W7+6ocy2xhDVQVh/KvC2Uf7kACs+gDytdusDSdWfWkaNzw==", "requires": {"@types/react": "*"}}, "@types/scheduler": {"version": "0.16.2", "resolved": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.2.tgz", "integrity": "sha512-hppQEBDmlwhFAXKJX2KnWLYu5yMfi91yazPb2l+lbJiwW+wdo1gNeRA+3RgNSO39WYX2euey41KEwnqesU2Jew=="}, "@types/trusted-types": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/@types/trusted-types/-/trusted-types-2.0.7.tgz", "integrity": "sha512-ScaPdn1dQczgbl0QFTeTOmVHFULt394XJgOQNoyVhZ6r2vLnMLJfBPd53SB52T/3G36VI1/g2MZaX0cwDuXsfw==", "devOptional": true}, "@vitejs/plugin-react": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.3.4.tgz", "integrity": "sha512-SCCPBJtYLdE8PX/7ZQAs1QAZ8Jqwih+0VBLum1EGqmCCQal+MIUqLCzj3ZUy8ufbC0cAM4LRlSTm7IQJwWT4ug==", "dev": true, "requires": {"@babel/core": "^7.26.0", "@babel/plugin-transform-react-jsx-self": "^7.25.9", "@babel/plugin-transform-react-jsx-source": "^7.25.9", "@types/babel__core": "^7.20.5", "react-refresh": "^0.14.2"}}, "browserslist": {"version": "4.24.4", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.24.4.tgz", "integrity": "sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A==", "dev": true, "requires": {"caniuse-lite": "^1.0.30001688", "electron-to-chromium": "^1.5.73", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.1"}}, "caniuse-lite": {"version": "1.0.30001702", "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001702.tgz", "integrity": "sha512-LoPe/D7zioC0REI5W73PeR1e1MLCipRGq/VkovJnd6Df+QVqT+vT33OXCp8QUd7kA7RZrHWxb1B36OQKI/0gOA==", "dev": true}, "convert-source-map": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz", "integrity": "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==", "dev": true}, "csstype": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz", "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw=="}, "debug": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz", "integrity": "sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==", "dev": true, "requires": {"ms": "^2.1.3"}}, "dom-helpers": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz", "integrity": "sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==", "requires": {"@babel/runtime": "^7.8.7", "csstype": "^3.0.2"}}, "dompurify": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/dompurify/-/dompurify-3.2.4.tgz", "integrity": "sha512-ysFSFEDVduQpyhzAob/kkuJjf5zWkZD8/A9ywSp1byueyuCfHamrCBa14/Oc2iiB0e51B+NpxSl5gmzn+Ms/mg==", "requires": {"@types/trusted-types": "^2.0.7"}}, "electron-to-chromium": {"version": "1.5.112", "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.112.tgz", "integrity": "sha512-oen93kVyqSb3l+ziUgzIOlWt/oOuy4zRmpwestMn4rhFWAoFJeFuCVte9F2fASjeZZo7l/Cif9TiyrdW4CwEMA==", "dev": true}, "esbuild": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/esbuild/-/esbuild-0.25.0.tgz", "integrity": "sha512-BXq5mqc8ltbaN34cDqWuYKyNhX8D/Z0J1xdtdQ8UcIIIyJyz+ZMKUt58tF3SrZ85jcfN/PZYhjR5uDQAYNVbuw==", "dev": true, "requires": {"@esbuild/aix-ppc64": "0.25.0", "@esbuild/android-arm": "0.25.0", "@esbuild/android-arm64": "0.25.0", "@esbuild/android-x64": "0.25.0", "@esbuild/darwin-arm64": "0.25.0", "@esbuild/darwin-x64": "0.25.0", "@esbuild/freebsd-arm64": "0.25.0", "@esbuild/freebsd-x64": "0.25.0", "@esbuild/linux-arm": "0.25.0", "@esbuild/linux-arm64": "0.25.0", "@esbuild/linux-ia32": "0.25.0", "@esbuild/linux-loong64": "0.25.0", "@esbuild/linux-mips64el": "0.25.0", "@esbuild/linux-ppc64": "0.25.0", "@esbuild/linux-riscv64": "0.25.0", "@esbuild/linux-s390x": "0.25.0", "@esbuild/linux-x64": "0.25.0", "@esbuild/netbsd-arm64": "0.25.0", "@esbuild/netbsd-x64": "0.25.0", "@esbuild/openbsd-arm64": "0.25.0", "@esbuild/openbsd-x64": "0.25.0", "@esbuild/sunos-x64": "0.25.0", "@esbuild/win32-arm64": "0.25.0", "@esbuild/win32-ia32": "0.25.0", "@esbuild/win32-x64": "0.25.0"}}, "escalade": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "dev": true}, "estree-walker": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz", "integrity": "sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w=="}, "fsevents": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "optional": true}, "function-bind": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="}, "gensync": {"version": "1.0.0-beta.2", "resolved": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==", "dev": true}, "globals": {"version": "11.12.0", "resolved": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz", "integrity": "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==", "dev": true}, "has": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/has/-/has-1.0.3.tgz", "integrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==", "requires": {"function-bind": "^1.1.1"}}, "is-core-module": {"version": "2.13.0", "resolved": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.13.0.tgz", "integrity": "sha512-Z7dk6Qo8pOCp3l4tsX2C5ZVas4V+UxwQodwZhLopL91TX8UyyHEXafPcyoeeWuLrwzHcr3igO78wNLwHJHsMCQ==", "requires": {"has": "^1.0.3"}}, "js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="}, "jsesc": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz", "integrity": "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==", "dev": true}, "json5": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "dev": true}, "keyborg": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/keyborg/-/keyborg-2.4.1.tgz", "integrity": "sha512-B9EZwDd36WKlIq6JmimaTsTDx5E0aUqZcxtgTfK66ut1FbRXYhBmiB7Al2qKzB7CCX9C49sTBiiyVzsXCA6J4Q=="}, "loose-envify": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "requires": {"js-tokens": "^3.0.0 || ^4.0.0"}}, "lru-cache": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==", "dev": true, "requires": {"yallist": "^3.0.2"}}, "ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "dev": true}, "nanoid": {"version": "3.3.8", "resolved": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.8.tgz", "integrity": "sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==", "dev": true}, "ndjson-readablestream": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/ndjson-readablestream/-/ndjson-readablestream-1.0.7.tgz", "integrity": "sha512-4DDTwYTV4yRnCoXparQTF3JeahTNkLVy7XlA0RHXzAqQ3uU8vcu97bNW8rXAQOKQVJGs2aZoX+7cbvfs0LENEA=="}, "node-releases": {"version": "2.0.19", "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz", "integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==", "dev": true}, "object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg=="}, "path-parse": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="}, "picocolors": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "dev": true}, "picomatch": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="}, "postcss": {"version": "8.5.3", "resolved": "https://registry.npmjs.org/postcss/-/postcss-8.5.3.tgz", "integrity": "sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A==", "dev": true, "requires": {"nanoid": "^3.3.8", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}}, "prettier": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/prettier/-/prettier-3.0.3.tgz", "integrity": "sha512-L/4pUDMxcNa8R/EthV08Zt42WBO4h1rarVtK0K+QJG0X187OLo7l699jWw0GKuwzkPQ//jMFA/8Xm6Fh3J/DAg==", "dev": true}, "prop-types": {"version": "15.8.1", "resolved": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz", "integrity": "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==", "requires": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}, "dependencies": {"react-is": {"version": "16.13.1", "resolved": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz", "integrity": "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="}}}, "react": {"version": "18.2.0", "resolved": "https://registry.npmjs.org/react/-/react-18.2.0.tgz", "integrity": "sha512-/3IjMdb2L9QbBdWiW5e3P2/npwMBaU9mHCSCUzNln0ZCYbcfTsGbTJrU/kGemdH2IWmB2ioZ+zkxtmq6g09fGQ==", "requires": {"loose-envify": "^1.1.0"}}, "react-dom": {"version": "18.2.0", "resolved": "https://registry.npmjs.org/react-dom/-/react-dom-18.2.0.tgz", "integrity": "sha512-6IMTriUmvsjHUjNtEDudZfuDQUoWXVxKHhlEGSk81n4YFS+r/Kl99wXiwlVXtPBtJenozv2P+hxDsw9eA7Xo6g==", "requires": {"loose-envify": "^1.1.0", "scheduler": "^0.23.0"}, "dependencies": {"scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "requires": {"loose-envify": "^1.1.0"}}}}, "react-is": {"version": "17.0.2", "resolved": "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz", "integrity": "sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w=="}, "react-refresh": {"version": "0.14.2", "resolved": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.14.2.tgz", "integrity": "sha512-jCvmsr+1IUSMUyzOkRcvnVbX3ZYC6g9TDrDbFuFmRDq7PD4yaGbLKNQL6k2jnArV8hjYxh7hVhAZB6s9HDGpZA==", "dev": true}, "react-router": {"version": "6.21.3", "resolved": "https://registry.npmjs.org/react-router/-/react-router-6.21.3.tgz", "integrity": "sha512-a0H638ZXULv1OdkmiK6s6itNhoy33ywxmUFT/xtSoVyf9VnC7n7+VT4LjVzdIHSaF5TIh9ylUgxMXksHTgGrKg==", "requires": {"@remix-run/router": "1.14.2"}}, "react-router-dom": {"version": "6.21.3", "resolved": "https://registry.npmjs.org/react-router-dom/-/react-router-dom-6.21.3.tgz", "integrity": "sha512-kNzubk7n4YHSrErzjLK72j0B5i969GsuCGazRl3G6j1zqZBLjuSlYBdVdkDOgzGdPIffUOc9nmgiadTEVoq91g==", "requires": {"@remix-run/router": "1.14.2", "react-router": "6.21.3"}}, "react-transition-group": {"version": "4.4.5", "resolved": "https://registry.npmjs.org/react-transition-group/-/react-transition-group-4.4.5.tgz", "integrity": "sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==", "requires": {"@babel/runtime": "^7.5.5", "dom-helpers": "^5.0.1", "loose-envify": "^1.4.0", "prop-types": "^15.6.2"}}, "regenerator-runtime": {"version": "0.14.1", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz", "integrity": "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw=="}, "resolve": {"version": "1.22.4", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.22.4.tgz", "integrity": "sha512-PXNdCiPqDqeUou+w1C2eTQbNfxKSuMxqTCuvlmmMsk1NWHL5fRrhY6Pl0qEYYc6+QqGClco1Qj8XnjPego4wfg==", "requires": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}, "rollup": {"version": "3.29.5", "resolved": "https://registry.npmjs.org/rollup/-/rollup-3.29.5.tgz", "integrity": "sha512-GVsDdsbJzzy4S/v3dqWPJ7EfvZJfCHiDqe80IyrF59LYuP+e6U1LJoUqeuqRbwAWoMNoXivMNeNAOf5E22VA1w==", "requires": {"fsevents": "~2.3.2"}}, "rtl-css-js": {"version": "1.16.1", "resolved": "https://registry.npmjs.org/rtl-css-js/-/rtl-css-js-1.16.1.tgz", "integrity": "sha512-lRQgou1mu19e+Ya0LsTvKrVJ5TYUbqCVPAiImX3UfLTenarvPUl1QFdvu5Z3PYmHT9RCcwIfbjRQBntExyj3Zg==", "requires": {"@babel/runtime": "^7.1.2"}}, "scheduler": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.20.2.tgz", "integrity": "sha512-2eWfGgAqqWFGqtdMmcL5zCMK1U8KlXv8SQFGglL3CEtd0aDVDWgeF/YoCmvln55m5zSk3J/20hTaSBeSObsQDQ==", "requires": {"loose-envify": "^1.1.0", "object-assign": "^4.1.1"}}, "semver": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "dev": true}, "source-map-js": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==", "dev": true}, "stylis": {"version": "4.3.1", "resolved": "https://registry.npmjs.org/stylis/-/stylis-4.3.1.tgz", "integrity": "sha512-EQepAV+wMsIaGVGX1RECzgrcqRRU/0sYOHkeLsZ3fzHaHXZy4DaOOX0vOlGQdlsjkh3mFHAIlVimpwAs4dslyQ=="}, "supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="}, "tabster": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/tabster/-/tabster-5.2.0.tgz", "integrity": "sha512-cSi3a0gGeM9Co/gTKHlhTFfiitwVjcA+kP9lJux0U7QaRrZox1yYrfbwZhJXM7N0fux7BgvCYaOxME5k0EQ0tA==", "requires": {"keyborg": "^2.2.0", "tslib": "^2.3.1"}}, "tinyglobby": {"version": "0.2.12", "resolved": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.12.tgz", "integrity": "sha512-qkf4trmKSIiMTs/E63cxH+ojC2unam7rJ0WrauAzpT3ECNTxGRMlaXxVbfxMUC/w0LaYk6jQ4y/nGR9uBO3tww==", "dev": true, "requires": {"fdir": "^6.4.3", "picomatch": "^4.0.2"}, "dependencies": {"fdir": {"version": "6.4.3", "resolved": "https://registry.npmjs.org/fdir/-/fdir-6.4.3.tgz", "integrity": "sha512-PMXmW2y1hDDfTSRc9gaXIuCCRpuoz3Kaz8cUelp3smouvfT632ozg2vrT6lJsHKKOF59YLbOGfAWGUcKEfRMQw==", "dev": true, "requires": {}}, "picomatch": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-4.0.2.tgz", "integrity": "sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==", "dev": true}}}, "tslib": {"version": "2.5.0", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.5.0.tgz", "integrity": "sha512-336iVw3rtn2BUK7ORdIAHTyxHGRIHVReokCR3XjbckJMK7ms8FysBfhLR8IXnAgy7T0PTPNBWKiH514FOW/WSg=="}, "typescript": {"version": "5.2.2", "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.2.2.tgz", "integrity": "sha512-mI4WrpHsbCIcwT9cF4FZvr80QUeKvsUsUvKDoR+X/7XHQH98xYD8YHZg7ANtz2GtZt/CBq2QJ0thkGJMHfqc1w=="}, "update-browserslist-db": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "integrity": "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==", "dev": true, "requires": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}}, "use-disposable": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/use-disposable/-/use-disposable-1.0.2.tgz", "integrity": "sha512-UMaXVlV77dWOu4GqAFNjRzHzowYKUKbJBQfCexvahrYeIz4OkUYUjna4Tjjdf92NH8Nm8J7wEfFRgTIwYjO5jg==", "requires": {}}, "vite": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/vite/-/vite-6.3.1.tgz", "integrity": "sha512-kkzzkqtMESYklo96HKKPE5KKLkC1amlsqt+RjFMlX2AvbRB/0wghap19NdBxxwGZ+h/C6DLCrcEphPIItlGrRQ==", "dev": true, "requires": {"esbuild": "^0.25.0", "fdir": "^6.4.3", "fsevents": "~2.3.3", "picomatch": "^4.0.2", "postcss": "^8.5.3", "rollup": "^4.34.9", "tinyglobby": "^0.2.12"}, "dependencies": {"fdir": {"version": "6.4.3", "resolved": "https://registry.npmjs.org/fdir/-/fdir-6.4.3.tgz", "integrity": "sha512-PMXmW2y1hDDfTSRc9gaXIuCCRpuoz3Kaz8cUelp3smouvfT632ozg2vrT6lJsHKKOF59YLbOGfAWGUcKEfRMQw==", "dev": true, "requires": {}}, "picomatch": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-4.0.2.tgz", "integrity": "sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==", "dev": true}, "rollup": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/rollup/-/rollup-4.34.9.tgz", "integrity": "sha512-nF5XYqWWp9hx/LrpC8sZvvvmq0TeTjQgaZHYmAgwysT9nh8sWnZhBnM8ZyVbbJFIQBLwHDNoMqsBZBbUo4U8sQ==", "dev": true, "requires": {"@rollup/rollup-android-arm-eabi": "4.34.9", "@rollup/rollup-android-arm64": "4.34.9", "@rollup/rollup-darwin-arm64": "4.34.9", "@rollup/rollup-darwin-x64": "4.34.9", "@rollup/rollup-freebsd-arm64": "4.34.9", "@rollup/rollup-freebsd-x64": "4.34.9", "@rollup/rollup-linux-arm-gnueabihf": "4.34.9", "@rollup/rollup-linux-arm-musleabihf": "4.34.9", "@rollup/rollup-linux-arm64-gnu": "4.34.9", "@rollup/rollup-linux-arm64-musl": "4.34.9", "@rollup/rollup-linux-loongarch64-gnu": "4.34.9", "@rollup/rollup-linux-powerpc64le-gnu": "4.34.9", "@rollup/rollup-linux-riscv64-gnu": "4.34.9", "@rollup/rollup-linux-s390x-gnu": "4.34.9", "@rollup/rollup-linux-x64-gnu": "4.34.9", "@rollup/rollup-linux-x64-musl": "4.34.9", "@rollup/rollup-win32-arm64-msvc": "4.34.9", "@rollup/rollup-win32-ia32-msvc": "4.34.9", "@rollup/rollup-win32-x64-msvc": "4.34.9", "@types/estree": "1.0.6", "fsevents": "~2.3.2"}}}}, "yallist": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==", "dev": true}}}, "fsevents": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "optional": true}, "function-bind": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="}, "gensync": {"version": "1.0.0-beta.2", "resolved": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==", "dev": true}, "globals": {"version": "11.12.0", "resolved": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz", "integrity": "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==", "dev": true}, "has": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/has/-/has-1.0.3.tgz", "integrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==", "requires": {"function-bind": "^1.1.1"}}, "is-core-module": {"version": "2.13.0", "resolved": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.13.0.tgz", "integrity": "sha512-Z7dk6Qo8pOCp3l4tsX2C5ZVas4V+UxwQodwZhLopL91TX8UyyHEXafPcyoeeWuLrwzHcr3igO78wNLwHJHsMCQ==", "requires": {"has": "^1.0.3"}}, "js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="}, "jsesc": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz", "integrity": "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==", "dev": true}, "json5": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "dev": true}, "keyborg": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/keyborg/-/keyborg-2.4.1.tgz", "integrity": "sha512-B9EZwDd36WKlIq6JmimaTsTDx5E0aUqZcxtgTfK66ut1FbRXYhBmiB7Al2qKzB7CCX9C49sTBiiyVzsXCA6J4Q=="}, "loose-envify": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "requires": {"js-tokens": "^3.0.0 || ^4.0.0"}}, "lru-cache": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==", "dev": true, "requires": {"yallist": "^3.0.2"}}, "ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "dev": true}, "nanoid": {"version": "3.3.8", "resolved": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.8.tgz", "integrity": "sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==", "dev": true}, "ndjson-readablestream": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/ndjson-readablestream/-/ndjson-readablestream-1.0.7.tgz", "integrity": "sha512-4DDTwYTV4yRnCoXparQTF3JeahTNkLVy7XlA0RHXzAqQ3uU8vcu97bNW8rXAQOKQVJGs2aZoX+7cbvfs0LENEA=="}, "node-releases": {"version": "2.0.19", "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz", "integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==", "dev": true}, "object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg=="}, "path-parse": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="}, "picocolors": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "dev": true}, "picomatch": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="}, "postcss": {"version": "8.5.3", "resolved": "https://registry.npmjs.org/postcss/-/postcss-8.5.3.tgz", "integrity": "sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A==", "dev": true, "requires": {"nanoid": "^3.3.8", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}}, "prettier": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/prettier/-/prettier-3.0.3.tgz", "integrity": "sha512-L/4pUDMxcNa8R/EthV08Zt42WBO4h1rarVtK0K+QJG0X187OLo7l699jWw0GKuwzkPQ//jMFA/8Xm6Fh3J/DAg==", "dev": true}, "prop-types": {"version": "15.8.1", "resolved": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz", "integrity": "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==", "requires": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}, "dependencies": {"react-is": {"version": "16.13.1", "resolved": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz", "integrity": "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="}}}, "react": {"version": "18.2.0", "resolved": "https://registry.npmjs.org/react/-/react-18.2.0.tgz", "integrity": "sha512-/3IjMdb2L9QbBdWiW5e3P2/npwMBaU9mHCSCUzNln0ZCYbcfTsGbTJrU/kGemdH2IWmB2ioZ+zkxtmq6g09fGQ==", "requires": {"loose-envify": "^1.1.0"}}, "react-dom": {"version": "18.2.0", "resolved": "https://registry.npmjs.org/react-dom/-/react-dom-18.2.0.tgz", "integrity": "sha512-6IMTriUmvsjHUjNtEDudZfuDQUoWXVxKHhlEGSk81n4YFS+r/Kl99wXiwlVXtPBtJenozv2P+hxDsw9eA7Xo6g==", "requires": {"loose-envify": "^1.1.0", "scheduler": "^0.23.0"}, "dependencies": {"scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "requires": {"loose-envify": "^1.1.0"}}}}, "react-is": {"version": "17.0.2", "resolved": "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz", "integrity": "sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w=="}, "react-refresh": {"version": "0.14.2", "resolved": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.14.2.tgz", "integrity": "sha512-jCvmsr+1IUSMUyzOkRcvnVbX3ZYC6g9TDrDbFuFmRDq7PD4yaGbLKNQL6k2jnArV8hjYxh7hVhAZB6s9HDGpZA==", "dev": true}, "react-router": {"version": "6.21.3", "resolved": "https://registry.npmjs.org/react-router/-/react-router-6.21.3.tgz", "integrity": "sha512-a0H638ZXULv1OdkmiK6s6itNhoy33ywxmUFT/xtSoVyf9VnC7n7+VT4LjVzdIHSaF5TIh9ylUgxMXksHTgGrKg==", "requires": {"@remix-run/router": "1.14.2"}}, "react-router-dom": {"version": "6.21.3", "resolved": "https://registry.npmjs.org/react-router-dom/-/react-router-dom-6.21.3.tgz", "integrity": "sha512-kNzubk7n4YHSrErzjLK72j0B5i969GsuCGazRl3G6j1zqZBLjuSlYBdVdkDOgzGdPIffUOc9nmgiadTEVoq91g==", "requires": {"@remix-run/router": "1.14.2", "react-router": "6.21.3"}}, "react-transition-group": {"version": "4.4.5", "resolved": "https://registry.npmjs.org/react-transition-group/-/react-transition-group-4.4.5.tgz", "integrity": "sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==", "requires": {"@babel/runtime": "^7.5.5", "dom-helpers": "^5.0.1", "loose-envify": "^1.4.0", "prop-types": "^15.6.2"}}, "regenerator-runtime": {"version": "0.14.1", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz", "integrity": "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw=="}, "resolve": {"version": "1.22.4", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.22.4.tgz", "integrity": "sha512-PXNdCiPqDqeUou+w1C2eTQbNfxKSuMxqTCuvlmmMsk1NWHL5fRrhY6Pl0qEYYc6+QqGClco1Qj8XnjPego4wfg==", "requires": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}, "rollup": {"version": "3.29.5", "resolved": "https://registry.npmjs.org/rollup/-/rollup-3.29.5.tgz", "integrity": "sha512-GVsDdsbJzzy4S/v3dqWPJ7EfvZJfCHiDqe80IyrF59LYuP+e6U1LJoUqeuqRbwAWoMNoXivMNeNAOf5E22VA1w==", "requires": {"fsevents": "~2.3.2"}}, "rtl-css-js": {"version": "1.16.1", "resolved": "https://registry.npmjs.org/rtl-css-js/-/rtl-css-js-1.16.1.tgz", "integrity": "sha512-lRQgou1mu19e+Ya0LsTvKrVJ5TYUbqCVPAiImX3UfLTenarvPUl1QFdvu5Z3PYmHT9RCcwIfbjRQBntExyj3Zg==", "requires": {"@babel/runtime": "^7.1.2"}}, "scheduler": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.20.2.tgz", "integrity": "sha512-2eWfGgAqqWFGqtdMmcL5zCMK1U8KlXv8SQFGglL3CEtd0aDVDWgeF/YoCmvln55m5zSk3J/20hTaSBeSObsQDQ==", "requires": {"loose-envify": "^1.1.0", "object-assign": "^4.1.1"}}, "semver": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "dev": true}, "source-map-js": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==", "dev": true}, "stylis": {"version": "4.3.1", "resolved": "https://registry.npmjs.org/stylis/-/stylis-4.3.1.tgz", "integrity": "sha512-EQepAV+wMsIaGVGX1RECzgrcqRRU/0sYOHkeLsZ3fzHaHXZy4DaOOX0vOlGQdlsjkh3mFHAIlVimpwAs4dslyQ=="}, "supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="}, "tabster": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/tabster/-/tabster-5.2.0.tgz", "integrity": "sha512-cSi3a0gGeM9Co/gTKHlhTFfiitwVjcA+kP9lJux0U7QaRrZox1yYrfbwZhJXM7N0fux7BgvCYaOxME5k0EQ0tA==", "requires": {"keyborg": "^2.2.0", "tslib": "^2.3.1"}}, "tinyglobby": {"version": "0.2.12", "resolved": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.12.tgz", "integrity": "sha512-qkf4trmKSIiMTs/E63cxH+ojC2unam7rJ0WrauAzpT3ECNTxGRMlaXxVbfxMUC/w0LaYk6jQ4y/nGR9uBO3tww==", "dev": true, "requires": {"fdir": "^6.4.3", "picomatch": "^4.0.2"}, "dependencies": {"fdir": {"version": "6.4.3", "resolved": "https://registry.npmjs.org/fdir/-/fdir-6.4.3.tgz", "integrity": "sha512-PMXmW2y1hDDfTSRc9gaXIuCCRpuoz3Kaz8cUelp3smouvfT632ozg2vrT6lJsHKKOF59YLbOGfAWGUcKEfRMQw==", "dev": true, "requires": {}}, "picomatch": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-4.0.2.tgz", "integrity": "sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==", "dev": true}}}, "tslib": {"version": "2.5.0", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.5.0.tgz", "integrity": "sha512-336iVw3rtn2BUK7ORdIAHTyxHGRIHVReokCR3XjbckJMK7ms8FysBfhLR8IXnAgy7T0PTPNBWKiH514FOW/WSg=="}, "typescript": {"version": "5.2.2", "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.2.2.tgz", "integrity": "sha512-mI4WrpHsbCIcwT9cF4FZvr80QUeKvsUsUvKDoR+X/7XHQH98xYD8YHZg7ANtz2GtZt/CBq2QJ0thkGJMHfqc1w=="}, "update-browserslist-db": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "integrity": "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==", "dev": true, "requires": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}}, "use-disposable": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/use-disposable/-/use-disposable-1.0.2.tgz", "integrity": "sha512-UMaXVlV77dWOu4GqAFNjRzHzowYKUKbJBQfCexvahrYeIz4OkUYUjna4Tjjdf92NH8Nm8J7wEfFRgTIwYjO5jg==", "requires": {}}, "vite": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/vite/-/vite-6.3.1.tgz", "integrity": "sha512-kkzzkqtMESYklo96HKKPE5KKLkC1amlsqt+RjFMlX2AvbRB/0wghap19NdBxxwGZ+h/C6DLCrcEphPIItlGrRQ==", "dev": true, "requires": {"esbuild": "^0.25.0", "fdir": "^6.4.3", "fsevents": "~2.3.3", "picomatch": "^4.0.2", "postcss": "^8.5.3", "rollup": "^4.34.9", "tinyglobby": "^0.2.12"}, "dependencies": {"fdir": {"version": "6.4.3", "resolved": "https://registry.npmjs.org/fdir/-/fdir-6.4.3.tgz", "integrity": "sha512-PMXmW2y1hDDfTSRc9gaXIuCCRpuoz3Kaz8cUelp3smouvfT632ozg2vrT6lJsHKKOF59YLbOGfAWGUcKEfRMQw==", "dev": true, "requires": {}}, "picomatch": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-4.0.2.tgz", "integrity": "sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==", "dev": true}, "rollup": {"version": "4.34.9", "resolved": "https://registry.npmjs.org/rollup/-/rollup-4.34.9.tgz", "integrity": "sha512-nF5XYqWWp9hx/LrpC8sZvvvmq0TeTjQgaZHYmAgwysT9nh8sWnZhBnM8ZyVbbJFIQBLwHDNoMqsBZBbUo4U8sQ==", "dev": true, "requires": {"@rollup/rollup-android-arm-eabi": "4.34.9", "@rollup/rollup-android-arm64": "4.34.9", "@rollup/rollup-darwin-arm64": "4.34.9", "@rollup/rollup-darwin-x64": "4.34.9", "@rollup/rollup-freebsd-arm64": "4.34.9", "@rollup/rollup-freebsd-x64": "4.34.9", "@rollup/rollup-linux-arm-gnueabihf": "4.34.9", "@rollup/rollup-linux-arm-musleabihf": "4.34.9", "@rollup/rollup-linux-arm64-gnu": "4.34.9", "@rollup/rollup-linux-arm64-musl": "4.34.9", "@rollup/rollup-linux-loongarch64-gnu": "4.34.9", "@rollup/rollup-linux-powerpc64le-gnu": "4.34.9", "@rollup/rollup-linux-riscv64-gnu": "4.34.9", "@rollup/rollup-linux-s390x-gnu": "4.34.9", "@rollup/rollup-linux-x64-gnu": "4.34.9", "@rollup/rollup-linux-x64-musl": "4.34.9", "@rollup/rollup-win32-arm64-msvc": "4.34.9", "@rollup/rollup-win32-ia32-msvc": "4.34.9", "@rollup/rollup-win32-x64-msvc": "4.34.9", "@types/estree": "1.0.6", "fsevents": "~2.3.2"}}}}, "yallist": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==", "dev": true}}}