{"name": "frontend", "private": true, "version": "1.0.0-alpha", "type": "module", "engines": {"node": ">=14.0.0"}, "scripts": {"dev": "vite --port=8081", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@azure/msal-browser": "^3.1.0", "@azure/msal-react": "^2.0.4", "@fluentui/react": "^8.112.5", "@fluentui/react-components": "^9.37.3", "@fluentui/react-icons": "^2.0.221", "@react-spring/web": "^9.7.3", "dompurify": "^3.2.4", "frontend": "file:", "ndjson-readablestream": "^1.0.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "scheduler": "^0.20.2"}, "devDependencies": {"@types/dompurify": "^3.0.3", "@types/react": "^18.2.34", "@types/react-dom": "^18.2.14", "@vitejs/plugin-react": "^4.3.4", "prettier": "^3.0.3", "typescript": "^5.2.2", "vite": "^6.3.1"}}