services:
  frontend:
    image: agent-openai-java-banking-assistant/frontend
    build: ./frontend
    environment:
      REACT_APP_API_BASE_URL: "http://copilot:8080"
    ports:
      - "80:80"
  copilot:
    image: agent-openai-java-banking-assistant/copilot-backend
    build: ./copilot
    environment:
      - AZURE_STORAGE_ACCOUNT=${AZURE_STORAGE_ACCOUNT}
      - AZURE_STORAGE_CONTAINER=${AZURE_STORAGE_CONTAINER}
      - AZURE_OPENAI_CHATGPT_MODEL=${AZURE_OPENAI_CHATGPT_MODEL}
      - AZURE_OPENAI_SERVICE=${AZURE_OPENAI_SERVICE}
      - AZURE_OPENAI_CHATGPT_DEPLOYMENT=${AZURE_OPENAI_CHATGPT_DEPLOYMENT}
      - AZURE_DOCUMENT_INTELLIGENCE_SERVICE=${AZURE_DOCUMENT_INTELLIGENCE_SERVICE}
      - spring_profiles_active=docker
      - ACCOUNTS_API_SERVER_URL=http://account:8080
      - PAYMENTS_API_SERVER_URL=http://payment:8080
      - TRANSACTIONS_API_SERVER_URL=http://transaction:8080
      - AZURE_CLIENT_ID=${servicePrincipal}
      - AZURE_CLIENT_SECRET=${servicePrincipalPassword}
      - AZURE_TENANT_ID=${servicePrincipalTenant}
  account:
    image: agent-openai-java-banking-assistant/business-account
    build:
      context: ./business-api/account
  payment:
    image: agent-openai-java-banking-assistant/business-payment
    build:
      context: ./business-api/payment
    environment:
      - TRANSACTIONS_API_SERVER_URL=http://transaction:8080
  transaction:
    image: agent-openai-java-banking-assistant/business-transaction-history
    build:
      context: ./business-api/transactions-history


