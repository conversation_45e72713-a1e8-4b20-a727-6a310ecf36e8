{"nodes": [{"id": "user-interface", "type": "text", "x": 100, "y": 100, "width": 250, "height": 120, "text": "# 用户界面层\n\n**React前端应用**\n- 聊天界面\n- 图片上传功能\n- 支持发票/收据上传\n- 实时对话交互", "color": "1"}, {"id": "supervisor-agent", "type": "text", "x": 450, "y": 100, "width": 280, "height": 140, "text": "# 监督者智能体\n\n**SupervisorAgent**\n- 用户意图识别\n- 智能体路由选择\n- 单轮对话管理\n- 基于GPT-4o-mini/GPT-4o\n- 错误处理与澄清", "color": "2"}, {"id": "account-agent", "type": "text", "x": 200, "y": 350, "width": 220, "height": 130, "text": "# 账户智能体\n\n**AccountMCPAgent**\n- 账户信息查询\n- 余额查询\n- 支付方式管理\n- 受益人信息\n- HTML格式展示", "color": "3"}, {"id": "transaction-agent", "type": "text", "x": 450, "y": 350, "width": 220, "height": 130, "text": "# 交易智能体\n\n**TransactionHistoryMCPAgent**\n- 交易历史查询\n- 收支记录分析\n- 特定收款人搜索\n- 时间范围筛选\n- 表格化展示", "color": "4"}, {"id": "payment-agent", "type": "text", "x": 700, "y": 350, "width": 250, "height": 150, "text": "# 支付智能体\n\n**PaymentMCPAgent**\n- 支付请求处理\n- 发票OCR扫描\n- 支付确认流程\n- 资金充足性检查\n- 重复支付检测\n- 受益人验证", "color": "5"}, {"id": "mcp-layer", "type": "text", "x": 100, "y": 550, "width": 850, "height": 100, "text": "# MCP工具层 (Model Context Protocol)\n\n**Spring AI MCP集成** - 将业务API暴露为智能体工具\n- HTTP SSE传输协议 | 工具规范自动生成 | 参数提取与验证 | 实时通信", "color": "6"}, {"id": "account-service", "type": "text", "x": 100, "y": 700, "width": 200, "height": 120, "text": "# 账户服务\n\n**Account Service**\n- 用户账户管理\n- 支付方式配置\n- 受益人管理\n- REST API + MCP", "color": "3"}, {"id": "payment-service", "type": "text", "x": 350, "y": 700, "width": 200, "height": 120, "text": "# 支付服务\n\n**Payment Service**\n- 支付处理\n- 交易通知\n- 支付验证\n- REST API + MCP", "color": "5"}, {"id": "transaction-service", "type": "text", "x": 600, "y": 700, "width": 200, "height": 120, "text": "# 交易服务\n\n**Transaction Service**\n- 交易历史查询\n- 收款人搜索\n- 交易分析\n- REST API + MCP", "color": "4"}, {"id": "azure-services", "type": "text", "x": 850, "y": 700, "width": 200, "height": 120, "text": "# Azure服务\n\n**云服务集成**\n- Azure OpenAI\n- Document Intelligence\n- Blob Storage\n- Container Apps", "color": "1"}], "edges": [{"id": "user-to-supervisor", "fromNode": "user-interface", "toNode": "supervisor-agent", "label": "1. 用户请求", "color": "2"}, {"id": "supervisor-to-account", "fromNode": "supervisor-agent", "toNode": "account-agent", "label": "2a. 账户相关路由", "color": "3"}, {"id": "supervisor-to-transaction", "fromNode": "supervisor-agent", "toNode": "transaction-agent", "label": "2b. 交易相关路由", "color": "4"}, {"id": "supervisor-to-payment", "fromNode": "supervisor-agent", "toNode": "payment-agent", "label": "2c. 支付相关路由", "color": "5"}, {"id": "agents-to-mcp", "fromNode": "account-agent", "toNode": "mcp-layer", "label": "3. MCP工具调用", "color": "6"}, {"id": "transaction-to-mcp", "fromNode": "transaction-agent", "toNode": "mcp-layer", "label": "3. MCP工具调用", "color": "6"}, {"id": "payment-to-mcp", "fromNode": "payment-agent", "toNode": "mcp-layer", "label": "3. MCP工具调用", "color": "6"}, {"id": "mcp-to-account-service", "fromNode": "mcp-layer", "toNode": "account-service", "label": "4a. 账户API调用", "color": "3"}, {"id": "mcp-to-payment-service", "fromNode": "mcp-layer", "toNode": "payment-service", "label": "4b. 支付API调用", "color": "5"}, {"id": "mcp-to-transaction-service", "fromNode": "mcp-layer", "toNode": "transaction-service", "label": "4c. 交易API调用", "color": "4"}, {"id": "payment-to-azure", "fromNode": "payment-agent", "toNode": "azure-services", "label": "OCR & 存储", "color": "1"}]}