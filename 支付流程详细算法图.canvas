{"nodes": [{"id": "payment-start", "type": "text", "x": 100, "y": 50, "width": 200, "height": 80, "text": "# 支付请求开始\n\n用户发起支付请求\n（文本或图片）", "color": "1"}, {"id": "input-analysis", "type": "text", "x": 400, "y": 50, "width": 220, "height": 100, "text": "# 输入分析\n\n- 检测输入类型\n- 图片：触发OCR流程\n- 文本：解析支付信息\n- 提取关键字段", "color": "2"}, {"id": "ocr-processing", "type": "text", "x": 700, "y": 50, "width": 250, "height": 120, "text": "# OCR处理\n\n1. 上传到Blob Storage\n2. Document Intelligence分析\n3. 提取发票数据：\n   - 发票号码\n   - 收款人\n   - 金额\n   - 到期日期", "color": "3"}, {"id": "data-validation", "type": "text", "x": 400, "y": 200, "width": 220, "height": 120, "text": "# 数据验证\n\n- 必填字段检查\n- 金额格式验证\n- 收款人信息验证\n- 用户确认提取数据", "color": "4"}, {"id": "duplicate-check", "type": "text", "x": 100, "y": 350, "width": 200, "height": 120, "text": "# 重复支付检查\n\n1. 查询交易历史\n2. 匹配发票号码\n3. 检查收款人\n4. 验证金额\n5. 防重复支付", "color": "5"}, {"id": "account-verification", "type": "text", "x": 350, "y": 350, "width": 200, "height": 140, "text": "# 账户验证\n\n1. 获取用户账户\n2. 查询支付方式\n3. 检查账户余额\n4. 验证支付限额\n5. 风险评估", "color": "6"}, {"id": "beneficiary-check", "type": "text", "x": 600, "y": 350, "width": 200, "height": 140, "text": "# 受益人验证\n\n1. 检查注册受益人\n2. 银行转账验证\n3. 获取银行代码\n4. 验证账户信息\n5. 新受益人处理", "color": "7"}, {"id": "payment-method-selection", "type": "text", "x": 850, "y": 350, "width": 200, "height": 120, "text": "# 支付方式选择\n\n1. 展示可用方式\n2. 用户选择确认\n3. 余额充足性检查\n4. 支付限额验证", "color": "8"}, {"id": "final-confirmation", "type": "text", "x": 400, "y": 520, "width": 220, "height": 140, "text": "# 最终确认\n\n1. 展示支付详情\n2. 用户最终确认\n3. 生成支付描述\n4. 包含发票ID\n5. 确认支付金额", "color": "9"}, {"id": "payment-execution", "type": "text", "x": 100, "y": 700, "width": 200, "height": 120, "text": "# 支付执行\n\n1. 调用支付API\n2. 处理支付请求\n3. 生成交易记录\n4. 更新账户余额", "color": "10"}, {"id": "result-processing", "type": "text", "x": 350, "y": 700, "width": 200, "height": 120, "text": "# 结果处理\n\n1. 检查支付状态\n2. 成功：生成确认\n3. 失败：错误处理\n4. 通知用户结果", "color": "11"}, {"id": "response-formatting", "type": "text", "x": 600, "y": 700, "width": 200, "height": 120, "text": "# 响应格式化\n\n1. HTML表格展示\n2. 支付确认信息\n3. 交易参考号\n4. 用户友好格式", "color": "12"}, {"id": "error-handling", "type": "text", "x": 850, "y": 700, "width": 200, "height": 120, "text": "# 错误处理\n\n1. 异常捕获\n2. 错误分类\n3. 用户友好提示\n4. 重试机制", "color": "13"}], "edges": [{"id": "start-to-analysis", "fromNode": "payment-start", "toNode": "input-analysis", "label": "1. 输入处理", "color": "2"}, {"id": "analysis-to-ocr", "fromNode": "input-analysis", "toNode": "ocr-processing", "label": "2a. 图片路径", "color": "3"}, {"id": "analysis-to-validation", "fromNode": "input-analysis", "toNode": "data-validation", "label": "2b. 文本路径", "color": "4"}, {"id": "ocr-to-validation", "fromNode": "ocr-processing", "toNode": "data-validation", "label": "3. OCR结果", "color": "4"}, {"id": "validation-to-duplicate", "fromNode": "data-validation", "toNode": "duplicate-check", "label": "4a. 重复检查", "color": "5"}, {"id": "validation-to-account", "fromNode": "data-validation", "toNode": "account-verification", "label": "4b. 账户验证", "color": "6"}, {"id": "validation-to-beneficiary", "fromNode": "data-validation", "toNode": "beneficiary-check", "label": "4c. 受益人验证", "color": "7"}, {"id": "account-to-payment-method", "fromNode": "account-verification", "toNode": "payment-method-selection", "label": "5. 支付方式", "color": "8"}, {"id": "all-to-confirmation", "fromNode": "duplicate-check", "toNode": "final-confirmation", "label": "6. 汇总确认", "color": "9"}, {"id": "beneficiary-to-confirmation", "fromNode": "beneficiary-check", "toNode": "final-confirmation", "label": "6. 汇总确认", "color": "9"}, {"id": "payment-method-to-confirmation", "fromNode": "payment-method-selection", "toNode": "final-confirmation", "label": "6. 汇总确认", "color": "9"}, {"id": "confirmation-to-execution", "fromNode": "final-confirmation", "toNode": "payment-execution", "label": "7. 执行支付", "color": "10"}, {"id": "execution-to-result", "fromNode": "payment-execution", "toNode": "result-processing", "label": "8. 处理结果", "color": "11"}, {"id": "result-to-formatting", "fromNode": "result-processing", "toNode": "response-formatting", "label": "9a. 成功格式化", "color": "12"}, {"id": "result-to-error", "fromNode": "result-processing", "toNode": "error-handling", "label": "9b. 错误处理", "color": "13"}]}