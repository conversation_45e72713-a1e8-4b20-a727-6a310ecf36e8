{"nodes": [{"id": "frontend", "type": "text", "x": 100, "y": 50, "width": 200, "height": 100, "text": "# React前端\n\n**HTTP/WebSocket**\n- REST API调用\n- 文件上传\n- 实时响应", "color": "1"}, {"id": "copilot-backend", "type": "text", "x": 400, "y": 50, "width": 250, "height": 120, "text": "# Copilot后端\n\n**Spring Boot应用**\n- ChatController\n- SupervisorAgent\n- 智能体管理\n- MCP客户端", "color": "2"}, {"id": "supervisor-agent", "type": "text", "x": 200, "y": 200, "width": 200, "height": 100, "text": "# 监督者智能体\n\n**意图路由中心**\n- GPT-4o调用\n- 智能体选择\n- 错误处理", "color": "3"}, {"id": "domain-agents", "type": "text", "x": 500, "y": 200, "width": 250, "height": 120, "text": "# 专业智能体集群\n\n**MCPToolAgent**\n- AccountMCPAgent\n- PaymentMCPAgent\n- TransactionMCPAgent", "color": "4"}, {"id": "mcp-layer", "type": "text", "x": 100, "y": 350, "width": 650, "height": 80, "text": "# MCP通信层\n\n**HTTP SSE协议** | 工具发现机制 | 参数序列化 | 错误传播 | 连接管理", "color": "5"}, {"id": "account-service", "type": "text", "x": 50, "y": 470, "width": 150, "height": 100, "text": "# 账户服务\n\n**Spring Boot**\n- MCP服务器\n- 业务逻辑\n- 数据管理", "color": "6"}, {"id": "payment-service", "type": "text", "x": 250, "y": 470, "width": 150, "height": 100, "text": "# 支付服务\n\n**Spring Boot**\n- MCP服务器\n- 支付处理\n- 交易管理", "color": "7"}, {"id": "transaction-service", "type": "text", "x": 450, "y": 470, "width": 150, "height": 100, "text": "# 交易服务\n\n**Spring Boot**\n- MCP服务器\n- 历史查询\n- 数据分析", "color": "8"}, {"id": "azure-services", "type": "text", "x": 650, "y": 470, "width": 150, "height": 100, "text": "# Azure服务\n\n**云服务集成**\n- OpenAI API\n- Document AI\n- Blob Storage", "color": "9"}], "edges": [{"id": "frontend-to-backend", "fromNode": "frontend", "toNode": "copilot-backend", "label": "HTTP REST", "color": "2"}, {"id": "backend-to-supervisor", "fromNode": "copilot-backend", "toNode": "supervisor-agent", "label": "方法调用", "color": "3"}, {"id": "supervisor-to-agents", "fromNode": "supervisor-agent", "toNode": "domain-agents", "label": "动态路由", "color": "4"}, {"id": "agents-to-mcp", "fromNode": "domain-agents", "toNode": "mcp-layer", "label": "MCP调用", "color": "5"}, {"id": "mcp-to-account", "fromNode": "mcp-layer", "toNode": "account-service", "label": "HTTP SSE", "color": "6"}, {"id": "mcp-to-payment", "fromNode": "mcp-layer", "toNode": "payment-service", "label": "HTTP SSE", "color": "7"}, {"id": "mcp-to-transaction", "fromNode": "mcp-layer", "toNode": "transaction-service", "label": "HTTP SSE", "color": "8"}, {"id": "agents-to-azure", "fromNode": "domain-agents", "toNode": "azure-services", "label": "Azure SDK", "color": "9"}]}