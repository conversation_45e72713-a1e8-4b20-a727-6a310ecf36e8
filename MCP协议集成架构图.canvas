{"nodes": [{"id": "mcp-client", "type": "text", "x": 100, "y": 50, "width": 250, "height": 120, "text": "# MCP客户端\n\n**MCPToolAgent**\n- HTTP SSE传输\n- 工具发现机制\n- 自动参数映射\n- 错误处理与重试", "color": "1"}, {"id": "transport-layer", "type": "text", "x": 450, "y": 50, "width": 220, "height": 100, "text": "# 传输层\n\n**HttpMcpTransport**\n- Server-Sent Events\n- 长连接管理\n- 超时控制\n- 请求/响应日志", "color": "2"}, {"id": "tool-discovery", "type": "text", "x": 750, "y": 50, "width": 200, "height": 100, "text": "# 工具发现\n\n**listTools()**\n- 动态工具注册\n- 规范自动生成\n- 类型验证\n- 参数映射", "color": "3"}, {"id": "spring-ai-mcp", "type": "text", "x": 300, "y": 200, "width": 300, "height": 120, "text": "# Spring AI MCP服务器\n\n**MethodToolCallbackProvider**\n- @Tool注解自动扫描\n- 方法参数自动映射\n- JSON序列化/反序列化\n- 异常处理包装", "color": "4"}, {"id": "business-services", "type": "text", "x": 100, "y": 350, "width": 200, "height": 100, "text": "# 业务服务\n\n**AccountMCPService**\n- @Tool方法定义\n- 业务逻辑封装\n- 数据验证\n- 错误处理", "color": "5"}, {"id": "payment-service", "type": "text", "x": 350, "y": 350, "width": 200, "height": 100, "text": "# 支付服务\n\n**PaymentMCPService**\n- 支付处理工具\n- 交易验证\n- 状态管理\n- 通知机制", "color": "6"}, {"id": "transaction-service", "type": "text", "x": 600, "y": 350, "width": 200, "height": 100, "text": "# 交易服务\n\n**TransactionMCPService**\n- 历史查询工具\n- 数据聚合\n- 报告生成\n- 分析功能", "color": "7"}, {"id": "error-handling", "type": "text", "x": 850, "y": 200, "width": 200, "height": 120, "text": "# 错误处理\n\n**统一异常处理**\n- 连接超时重试\n- 工具调用失败\n- 参数验证错误\n- 优雅降级", "color": "8"}, {"id": "monitoring", "type": "text", "x": 850, "y": 350, "width": 200, "height": 100, "text": "# 监控与日志\n\n**Application Insights**\n- 请求追踪\n- 性能监控\n- 错误统计\n- 调用链分析", "color": "9"}], "edges": [{"id": "client-to-transport", "fromNode": "mcp-client", "toNode": "transport-layer", "label": "1. 建立连接", "color": "2"}, {"id": "transport-to-discovery", "fromNode": "transport-layer", "toNode": "tool-discovery", "label": "2. 工具发现", "color": "3"}, {"id": "client-to-spring-ai", "fromNode": "mcp-client", "toNode": "spring-ai-mcp", "label": "3. 工具调用", "color": "4"}, {"id": "spring-ai-to-account", "fromNode": "spring-ai-mcp", "toNode": "business-services", "label": "4a. 账户工具", "color": "5"}, {"id": "spring-ai-to-payment", "fromNode": "spring-ai-mcp", "toNode": "payment-service", "label": "4b. 支付工具", "color": "6"}, {"id": "spring-ai-to-transaction", "fromNode": "spring-ai-mcp", "toNode": "transaction-service", "label": "4c. 交易工具", "color": "7"}, {"id": "transport-to-error", "fromNode": "transport-layer", "toNode": "error-handling", "label": "错误处理", "color": "8"}, {"id": "services-to-monitoring", "fromNode": "business-services", "toNode": "monitoring", "label": "监控日志", "color": "9"}, {"id": "payment-to-monitoring", "fromNode": "payment-service", "toNode": "monitoring", "label": "监控日志", "color": "9"}, {"id": "transaction-to-monitoring", "fromNode": "transaction-service", "toNode": "monitoring", "label": "监控日志", "color": "9"}]}