{"nodes": [{"id": "user-upload", "type": "text", "x": 100, "y": 50, "width": 200, "height": 80, "text": "# 用户上传\n\n\"请支付这张发票\"\n+ 图片文件", "color": "1"}, {"id": "chat-controller", "type": "text", "x": 350, "y": 50, "width": 200, "height": 100, "text": "# ChatController\n\n```java\nsupervisorAgent.invoke(\n  chatHistory)\n```", "color": "2"}, {"id": "supervisor-routing", "type": "text", "x": 600, "y": 50, "width": 220, "height": 120, "text": "# 监督者路由\n\n```java\nString nextAgent = \n  aiMessage.text();\n// \"PaymentAgent\"\n```", "color": "3"}, {"id": "payment-agent-invoke", "type": "text", "x": 350, "y": 200, "width": 200, "height": 100, "text": "# PaymentAgent调用\n\n```java\npaymentAgent.invoke(\n  chatHistory)\n```", "color": "4"}, {"id": "react-loop", "type": "text", "x": 100, "y": 350, "width": 250, "height": 140, "text": "# ReAct循环\n\n```java\nwhile (aiMessage.\n  hasToolExecutionRequests()) {\n  executeToolRequests();\n  // 继续推理\n}\n```", "color": "5"}, {"id": "ocr-tool", "type": "text", "x": 400, "y": 350, "width": 200, "height": 120, "text": "# OCR工具调用\n\n```java\nscanInvoice(filePath)\n// 提取发票数据\n```", "color": "6"}, {"id": "mcp-tools", "type": "text", "x": 650, "y": 350, "width": 200, "height": 140, "text": "# MCP工具调用\n\n```java\n// 检查重复支付\nsearchTransactions()\n// 获取账户信息\ngetAccountDetails()\n```", "color": "7"}, {"id": "user-confirmation", "type": "text", "x": 100, "y": 520, "width": 200, "height": 100, "text": "# 用户确认\n\n\"数据正确，\n使用我的Visa卡\"\n\"确认支付\"", "color": "8"}, {"id": "payment-execution", "type": "text", "x": 350, "y": 520, "width": 200, "height": 120, "text": "# 支付执行\n\n```java\nprocessPayment(\n  paymentRequest)\n// 返回确认信息\n```", "color": "9"}, {"id": "response-formatting", "type": "text", "x": 600, "y": 520, "width": 200, "height": 120, "text": "# 响应格式化\n\n```html\n<table>\n  <tr><th>状态</th>\n  <td>支付成功</td></tr>\n</table>\n```", "color": "10"}, {"id": "document-intelligence", "type": "text", "x": 900, "y": 200, "width": 200, "height": 120, "text": "# Azure Document Intelligence\n\n```java\nclient.beginAnalyzeDocument(\n  \"prebuilt-invoice\",\n  imageBytes)\n```", "color": "11"}, {"id": "business-services", "type": "text", "x": 900, "y": 350, "width": 200, "height": 140, "text": "# 业务服务\n\n```java\n@Tool\ngetAccountDetails(id)\n\n@Tool\nprocessPayment(payment)\n```", "color": "12"}], "edges": [{"id": "upload-to-controller", "fromNode": "user-upload", "toNode": "chat-controller", "label": "1. HTTP POST", "color": "2"}, {"id": "controller-to-supervisor", "fromNode": "chat-controller", "toNode": "supervisor-routing", "label": "2. 意图分析", "color": "3"}, {"id": "supervisor-to-payment", "fromNode": "supervisor-routing", "toNode": "payment-agent-invoke", "label": "3. 路由到PaymentAgent", "color": "4"}, {"id": "payment-to-react", "fromNode": "payment-agent-invoke", "toNode": "react-loop", "label": "4. 启动ReAct循环", "color": "5"}, {"id": "react-to-ocr", "fromNode": "react-loop", "toNode": "ocr-tool", "label": "5a. OCR扫描", "color": "6"}, {"id": "react-to-mcp", "fromNode": "react-loop", "toNode": "mcp-tools", "label": "5b. MCP工具", "color": "7"}, {"id": "ocr-to-azure", "fromNode": "ocr-tool", "toNode": "document-intelligence", "label": "Azure AI调用", "color": "11"}, {"id": "mcp-to-services", "fromNode": "mcp-tools", "toNode": "business-services", "label": "业务API调用", "color": "12"}, {"id": "user-confirm", "fromNode": "user-confirmation", "toNode": "payment-execution", "label": "6. 用户确认", "color": "9"}, {"id": "execution-to-formatting", "fromNode": "payment-execution", "toNode": "response-formatting", "label": "7. 格式化响应", "color": "10"}]}